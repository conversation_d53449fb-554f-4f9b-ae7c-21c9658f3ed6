# Instalar dependencias faltantes
install.packages(c('ltm', 'mirt'))
# Ahora debería funcionar sin problemas
install.packages(c('ltm', 'mirt'))
# Dependencias principales
install.packages(c(
"exams", "digest", "reticulate", "ggplot2", "dplyr", "purrr",
"stringr", "readr", "tibble", "magrittr", "rmarkdown", "bookdown",
"tinytex", "futile.logger", "memoise", "future", "furrr",
"parallel", "plotly", "DT", "shiny", "knitr", "yaml",
"jsonlite", "xml2"
))
# Dependencias psicométricas (las que faltaban)
install.packages(c("psychometric", "ltm", "mirt"))
# Verificar que se instalaron correctamente
library(ltm)
library(mirt)
library(psychometric)
# Si no hay errores, están instaladas correctamente
# Cargar el paquete
library(ICFESMathExams)
# Verificar versión
cat("📦 ICFESMathExams v", as.character(packageVersion("ICFESMathExams")), " instalado!\n")
# Probar funcionalidad básica
cat("🚀 Inicializando...\n")
config <- inicializar_icfes_optimizado(modo_operacion = "educativo")
cat("\n📊 Probando generación básica...\n")
ejercicio1 <- generar_datos("movimiento_lineal")
cat("✅ Ejercicio básico:", substr(ejercicio1$enunciado, 1, 50), "...\n")
cat("\n🔺 Probando geometría analítica...\n")
ejercicio2 <- generar_geometria_analitica("area_triangulo")
cat("✅ Geometría:", substr(ejercicio2$enunciado, 1, 50), "...\n")
cat("\n📈 Probando funciones matemáticas...\n")
ejercicio3 <- generar_funciones_matematicas("funcion_compuesta")
cat("✅ Funciones:", substr(ejercicio3$enunciado, 1, 50), "...\n")
cat("\n📝 Probando template r-exams...\n")
template_path <- crear_template_rexams(ejercicio2, "single_choice", "templates/", "test_final")
cat("✅ Template creado en:", template_path, "\n")
cat("\n🎉 ¡ICFESMathExams v3.0.0 funcionando perfectamente!\n")
# Datos generados por ICFESMathExams
# Tipo: area_triangulo
# Generado: 2025-07-04 23:05:42.44803
# Parámetros del ejercicio
# En RStudio, ejecutar:
source("08_ICFESMathExams/crear_examen_completo.R")
source("~/Tareas/926a5501-0440-4015-919c-0aadd5ccb734-files/08_ICFESMathExams/crear_examen_completo.R")
install.packages(c("gapminder", "magick", "packrat", "qpdf", "rsconnect"))
