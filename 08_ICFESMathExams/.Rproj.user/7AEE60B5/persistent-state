build-last-errors="[]"
build-last-errors-base-dir="~/Tareas/926a5501-0440-4015-919c-0aadd5ccb734-files/08_ICFESMathExams/"
build-last-outputs="[{\"type\":0,\"output\":\"==> R CMD INSTALL --preclean --no-multiarch --with-keep.source 08_ICFESMathExams\\n\\n\"},{\"type\":1,\"output\":\"* installing to library ‘/home/<USER>/R/x86_64-pc-linux-gnu-library/4.5/_build’\\n\"},{\"type\":1,\"output\":\"* installing *source* package ‘ICFESMathExams’ ...\\n\"},{\"type\":1,\"output\":\"** this is package ‘ICFESMathExams’ version ‘3.0.0’\\n\"},{\"type\":1,\"output\":\"** using staged installation\\n\"},{\"type\":1,\"output\":\"\"},{\"type\":1,\"output\":\"** R\\n\"},{\"type\":1,\"output\":\"** inst\\n\"},{\"type\":1,\"output\":\"** byte-compile and prepare package for lazy loading\\n\"},{\"type\":1,\"output\":\"\"},{\"type\":1,\"output\":\"No man pages found in package  ‘ICFESMathExams’ \\n\"},{\"type\":1,\"output\":\"\"},{\"type\":1,\"output\":\"** help\\n\"},{\"type\":1,\"output\":\"*** installing help indices\\n\"},{\"type\":1,\"output\":\"** building package indices\\n\"},{\"type\":1,\"output\":\"\"},{\"type\":1,\"output\":\"** installing vignettes\\n\"},{\"type\":1,\"output\":\"** testing if installed package can be loaded from temporary location\\n\"},{\"type\":1,\"output\":\"\"},{\"type\":1,\"output\":\"** testing if installed package can be loaded from final location\\n\"},{\"type\":1,\"output\":\"\"},{\"type\":1,\"output\":\"** testing if installed package keeps a record of temporary installation path\\n\"},{\"type\":1,\"output\":\"* DONE (ICFESMathExams)\\n\"},{\"type\":1,\"output\":\"\"}]"
compile_pdf_state="{\"tab_visible\":false,\"running\":false,\"target_file\":\"\",\"output\":\"\",\"errors\":[]}"
files.monitored-path=""
find-in-files-state="{\"handle\":\"\",\"input\":\"\",\"path\":\"\",\"regex\":false,\"ignoreCase\":false,\"results\":{\"file\":[],\"line\":[],\"lineValue\":[],\"matchOn\":[],\"matchOff\":[],\"replaceMatchOn\":[],\"replaceMatchOff\":[]},\"running\":false,\"replace\":false,\"preview\":false,\"gitFlag\":false,\"replacePattern\":\"\"}"
imageDirtyState="1"
saveActionState="0"
