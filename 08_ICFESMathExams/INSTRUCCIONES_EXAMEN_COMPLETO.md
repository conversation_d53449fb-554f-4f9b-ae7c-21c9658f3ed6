# 🎯 INSTRUCCIONES: Crear Examen Completo con ICFESMathExams v3.0.0

## 📋 **Resumen**
Este script te permite crear un examen completo basado en interpretación de gráficas de poblaciones de países, usando todas las funcionalidades avanzadas de ICFESMathExams v3.0.0.

## 🚀 **Cómo Ejecutar**

### **Paso 1: Abrir RStudio**
1. Abre RStudio
2. Asegúrate de que ICFESMathExams v3.0.0 esté instalado

### **Paso 2: Ejecutar el Script**
```r
# Opción A: Ejecutar todo el script
source("crear_examen_completo.R")

# Opción B: Ejecutar paso a paso (recomendado)
# Abre el archivo crear_examen_completo.R y ejecuta sección por sección
```

### **Paso 3: Verificar Resultados**
Después de ejecutar, tendrás:
- ✅ 8 ejercicios diversos generados
- ✅ Templates r-exams creados
- ✅ Examen adaptativo personalizado
- ✅ Archivos para Moodle listos
- ✅ Exportaciones múltiples (Canvas, QTI, etc.)

## 📁 **Archivos Generados**

### **Directorios Creados:**
```
📂 templates_examen_completo/
   ├── ejercicio_01_movimiento.Rmd
   ├── ejercicio_02_estadistica.Rmd
   ├── ejercicio_03_geometria.Rmd
   ├── ejercicio_04_funciones.Rmd
   ├── ejercicio_05_algebra.Rmd
   ├── ejercicio_06_graficas1.Rmd
   ├── ejercicio_07_graficas2.Rmd
   └── ejercicio_08_graficas3.Rmd

📂 examen_moodle/
   ├── Matemáticas ICFES - Grado 11_moodle.xml
   └── configuracion_moodle.txt

📂 output_examen_completo/
   ├── moodle/
   ├── canvas/
   └── qti/
```

## 🎯 **Funcionalidades Demostradas**

### **1. Nuevo Tipo de Ejercicio**
- **Interpretación de gráficas** basado en tu archivo original
- Poblaciones de países con contexto latinoamericano
- Aleatorización inteligente de países y años

### **2. Examen Adaptativo**
- Personalización por perfil de estudiante
- Algoritmo de competencias balanceadas
- Enfoque en debilidades identificadas

### **3. Integración r-exams**
- Templates automáticos .Rmd
- Compatibilidad total con r-exams
- Exportación a múltiples formatos

### **4. Exportación LMS**
- Moodle XML nativo
- Canvas QTI 2.1
- Blackboard compatible
- Metadatos completos

## 📊 **Ejemplo de Ejercicio Generado**

```
ENUNCIADO:
La siguiente gráfica muestra la evolución de la población (en millones 
de habitantes) de cinco países entre 1960 y 2013. Según la información 
presentada, ¿en qué año aproximadamente las poblaciones de Colombia y 
Brasil fueron iguales?

PAÍSES INCLUIDOS:
1. Colombia
2. Brasil  
3. España
4. Japón
5. Nigeria

OPCIONES:
A) 1986
B) 1995 ✓
C) 2001
D) 2008

SOLUCIÓN:
Analizando la gráfica, las líneas de Colombia y Brasil se intersectan 
aproximadamente en el año 1995.
```

## 🔧 **Personalización**

### **Modificar Perfil de Estudiante:**
```r
perfil_estudiante <- list(
  habilidad_estimada = 0.7,        # 0.0 - 1.0
  grado = 11,                      # 9, 10, 11
  fortalezas = c("algebra"),       # Áreas fuertes
  debilidades = c("graficas"),     # Áreas a mejorar
  contexto = "rural",              # "urbano", "rural"
  region = "Antioquia"             # Región colombiana
)
```

### **Cambiar Configuración de Examen:**
```r
config_examen <- list(
  titulo = "Tu Título Personalizado",
  tiempo_limite = 90,              # minutos
  num_versiones = 3,               # versiones diferentes
  formatos_salida = c("pdf", "html", "moodle")
)
```

### **Seleccionar Tipos de Ejercicios:**
```r
# Tipos disponibles:
tipos_disponibles <- c(
  "movimiento_lineal",
  "estadistica_descriptiva", 
  "geometria_analitica",
  "funciones_matematicas",
  "algebra_avanzada",
  "interpretacion_graficas"    # ¡NUEVO!
)
```

## 🎓 **Para Uso Educativo**

### **Importar a Moodle:**
1. Ve a tu curso en Moodle
2. Administración → Banco de preguntas → Importar
3. Selecciona el archivo XML generado
4. Formato: "Formato XML de Moodle"
5. Importar

### **Usar en Canvas:**
1. Ve a tu curso en Canvas
2. Configuración → Importar contenido del curso
3. Selecciona "QTI .zip file"
4. Sube el archivo QTI generado

### **Imprimir en PDF:**
1. Los templates .Rmd pueden compilarse directamente
2. Usa `rmarkdown::render()` para generar PDFs
3. Personaliza el formato según necesidades

## ⚠️ **Solución de Problemas**

### **Error: Paquete no encontrado**
```r
# Reinstalar ICFESMathExams
devtools::install_local("path/to/ICFESMathExams")
library(ICFESMathExams)
```

### **Error: Directorio no existe**
```r
# El script crea directorios automáticamente
# Si hay problemas, crear manualmente:
dir.create("templates_examen_completo", showWarnings = FALSE)
dir.create("examen_moodle", showWarnings = FALSE)
```

### **Error: Función no encontrada**
```r
# Verificar que todas las funciones estén disponibles
ls("package:ICFESMathExams")

# Reinicializar si es necesario
config <- inicializar_icfes_optimizado()
```

## 🚀 **Próximos Pasos**

1. **Ejecutar el script completo**
2. **Revisar archivos generados**
3. **Importar a tu LMS preferido**
4. **Personalizar según necesidades**
5. **Generar más versiones**
6. **Implementar con estudiantes reales**

## 📞 **Soporte**

Si encuentras problemas:
1. Verifica que ICFESMathExams v3.0.0 esté instalado
2. Revisa que todas las dependencias estén disponibles
3. Ejecuta paso a paso para identificar errores
4. Consulta la documentación del paquete

---

**¡Disfruta creando exámenes poderosos y efectivos con ICFESMathExams v3.0.0!** 🎉
