```{r data generation, echo = FALSE, results = "hide"}
# Template r-exams para ejercicios de estadística descriptiva
# Generado por ICFESMathExams

# Generar datos aleatorios
n <- sample(8:15, 1)  # Tamaño de muestra
media_poblacion <- sample(60:90, 1)
desviacion <- sample(8:15, 1)

# Generar muestra de calificaciones
set.seed(sample(1:1000, 1))
calificaciones <- round(rnorm(n, media_poblacion, desviacion))
calificaciones <- pmax(0, pmin(100, calificaciones))  # Limitar entre 0 y 100

# Calcular estadísticos
media_muestra <- round(mean(calificaciones), 1)
mediana_muestra <- round(median(calificaciones), 1)
moda_muestra <- as.numeric(names(sort(table(calificaciones), decreasing = TRUE))[1])

# Generar distractores
media_incorrecta1 <- round(media_muestra + sample(3:8, 1), 1)
media_incorrecta2 <- round(media_muestra - sample(2:6, 1), 1)
media_incorrecta3 <- round(sum(calificaciones), 1)  # Error común: suma en lugar de promedio

# Crear opciones
opciones <- c(media_muestra, media_incorrecta1, media_incorrecta2, media_incorrecta3)
opciones <- sample(opciones)

# Encontrar respuesta correcta
respuesta_correcta <- which(opciones == media_muestra)
solucion_binaria <- rep(0, 4)
solucion_binaria[respuesta_correcta] <- 1

# Crear tabla de datos para mostrar
datos_tabla <- paste(calificaciones, collapse = ", ")
```

Question
========

En una prueba de matemáticas aplicada a `r n` estudiantes de grado 11°, se obtuvieron las siguientes calificaciones (sobre 100):

**Calificaciones:** `r datos_tabla`

**¿Cuál es la media aritmética (promedio) de estas calificaciones?**

*Contexto ICFES:* Este ejercicio evalúa la competencia de interpretación y representación, específicamente el cálculo e interpretación de medidas de tendencia central, tema fundamental en las pruebas Saber 11°.

Answerlist
----------
* `r opciones[1]`
* `r opciones[2]`
* `r opciones[3]`
* `r opciones[4]`

Solution
========

Para calcular la **media aritmética** (promedio), sumamos todos los valores y dividimos entre el número total de datos.

**Fórmula:** Media = (Suma de todos los valores) ÷ (Número de valores)

**Paso a paso:**

1. **Sumar todas las calificaciones:**
   `r paste(calificaciones, collapse = " + ")` = `r sum(calificaciones)`

2. **Contar el número de estudiantes:**
   n = `r n` estudiantes

3. **Aplicar la fórmula:**
   Media = `r sum(calificaciones)` ÷ `r n` = **`r media_muestra`**

**Verificación:**
- Suma total: `r sum(calificaciones)`
- Número de datos: `r n`
- Media: `r sum(calificaciones)` ÷ `r n` = `r media_muestra`

**Información adicional:**
- Mediana: `r mediana_muestra`
- Moda: `r moda_muestra`
- Rango: `r max(calificaciones) - min(calificaciones)`

**Respuesta correcta:** `r media_muestra`

Meta-information
================
extype: schoice
exsolution: `r paste(solucion_binaria, collapse = "")`
exname: estadistica_descriptiva_icfes
extol: 0.1
expoints: 1
