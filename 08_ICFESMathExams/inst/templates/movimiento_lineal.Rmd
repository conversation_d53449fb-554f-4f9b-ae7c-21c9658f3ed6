```{r data generation, echo = FALSE, results = "hide"}
# Template r-exams para ejercicios de movimiento lineal
# Generado por ICFESMathExams

# Parámetros aleatorios
velocidad <- sample(20:80, 1)
tiempo <- sample(2:8, 1)
distancia <- velocidad * tiempo

# Generar distractores
distancia_incorrecta1 <- distancia + sample(10:30, 1)
distancia_incorrecta2 <- distancia - sample(5:15, 1)
distancia_incorrecta3 <- velocidad + tiempo

# Crear opciones de respuesta
opciones <- c(distancia, distancia_incorrecta1, distancia_incorrecta2, distancia_incorrecta3)
opciones <- sample(opciones)  # Mezclar opciones

# Encontrar posición de respuesta correcta
respuesta_correcta <- which(opciones == distancia)
solucion_binaria <- rep(0, 4)
solucion_binaria[respuesta_correcta] <- 1
```

Question
========

Un automóvil viaja a una velocidad constante de `r velocidad` km/h durante `r tiempo` horas. 

**¿Cuál es la distancia total recorrida?**

*Contexto:* Este tipo de problema es común en las pruebas ICFES de matemáticas, específicamente en la competencia de resolución de problemas con situaciones de movimiento rectilíneo uniforme.

Answerlist
----------
* `r opciones[1]` km
* `r opciones[2]` km  
* `r opciones[3]` km
* `r opciones[4]` km

Solution
========

Para resolver este problema de movimiento rectilíneo uniforme, aplicamos la fórmula básica:

**Distancia = Velocidad × Tiempo**

Datos del problema:
- Velocidad = `r velocidad` km/h
- Tiempo = `r tiempo` h

Sustituyendo en la fórmula:
Distancia = `r velocidad` km/h × `r tiempo` h = **`r distancia` km**

**Explicación paso a paso:**

1. **Identificar la fórmula:** Para movimiento rectilíneo uniforme (velocidad constante), usamos d = v × t

2. **Identificar los datos:**
   - v = `r velocidad` km/h
   - t = `r tiempo` h
   - d = ?

3. **Sustituir y calcular:**
   - d = `r velocidad` × `r tiempo`
   - d = `r distancia` km

4. **Verificar:** Las unidades son correctas (km/h × h = km)

**Respuesta correcta:** `r distancia` km

Meta-information
================
extype: schoice
exsolution: `r paste(solucion_binaria, collapse = "")`
exname: movimiento_lineal_icfes
extol: 0.01
expoints: 1
