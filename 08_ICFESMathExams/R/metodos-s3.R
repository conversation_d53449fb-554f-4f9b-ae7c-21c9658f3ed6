#' @title Métodos S3 para Clases ICFESMathExams
#' @description Métodos de impresión y resumen para las clases del paquete

#' Método print para examen adaptativo
#' @param x Objeto de clase examen_adaptativo_icfes
#' @param ... Argumentos adicionales
#' @export
print.examen_adaptativo_icfes <- function(x, ...) {
  cat("=== Examen Adaptativo ICFESMathExams ===\n")
  cat("Algoritmo usado:", x$algoritmo_usado, "\n")
  cat("Número de ejercicios:", length(x$ejercicios_seleccionados), "\n")
  cat("Dificultad promedio:", round(mean(x$secuencia_dificultad, na.rm = TRUE), 2), "\n")
  cat("Rango de dificultad:", paste(range(x$secuencia_dificultad, na.rm = TRUE), collapse = " - "), "\n")
  
  if (!is.null(x$metadatos)) {
    cat("Fecha de generación:", as.character(x$metadatos$fecha_generacion), "\n")
    if (!is.null(x$metadatos$competencias_cubiertas)) {
      cat("Competencias cubiertas:", length(x$metadatos$competencias_cubiertas), "\n")
    }
  }
  
  cat("\nEjercicios seleccionados:\n")
  for (i in seq_along(x$ejercicios_seleccionados)) {
    ejercicio <- x$ejercicios_seleccionados[[i]]
    tipo <- ejercicio$tipo %||% "Sin tipo"
    dificultad <- x$secuencia_dificultad[i] %||% "N/A"
    cat(sprintf("  %d. %s (Dificultad: %s)\n", i, tipo, dificultad))
  }
  
  invisible(x)
}

#' Método print para examen personalizado
#' @param x Objeto de clase examen_personalizado_icfes
#' @param ... Argumentos adicionales
#' @export
print.examen_personalizado_icfes <- function(x, ...) {
  cat("=== Examen Personalizado ICFESMathExams ===\n")
  cat("Número de ejercicios:", length(x$ejercicios), "\n")
  
  if (!is.null(x$metadatos)) {
    cat("Enfoque principal:", x$metadatos$enfoque_principal %||% "General", "\n")
    cat("Fecha de creación:", as.character(x$metadatos$fecha_creacion), "\n")
  }
  
  if (!is.null(x$analisis_perfil)) {
    cat("Perfil del estudiante analizado: ✓\n")
  }
  
  if (!is.null(x$recomendaciones_estudio)) {
    cat("Recomendaciones de estudio incluidas: ✓\n")
  }
  
  if (!is.null(x$plan_mejora)) {
    cat("Plan de mejora personalizado: ✓\n")
  }
  
  invisible(x)
}

#' Método print para análisis psicométrico
#' @param x Objeto de clase psicometria_icfes
#' @param ... Argumentos adicionales
#' @export
print.psicometria_icfes <- function(x, ...) {
  cat("=== Análisis Psicométrico ICFESMathExams ===\n")
  cat("Estudiantes analizados:", x$resumen_general$n_estudiantes, "\n")
  cat("Ítems analizados:", x$resumen_general$n_items, "\n")
  cat("Modelo IRT:", x$resumen_general$modelo_irt, "\n")
  cat("Fecha de análisis:", as.character(x$resumen_general$fecha_analisis), "\n\n")
  
  # Análisis clásico
  if (!is.null(x$analisis_clasico)) {
    cat("--- Análisis Clásico ---\n")
    cat("Alpha de Cronbach:", round(x$analisis_clasico$alpha_total, 3), "\n")
    cat("Dificultad promedio:", round(x$analisis_clasico$dificultad_promedio, 3), "\n")
    cat("Discriminación promedio:", round(x$analisis_clasico$discriminacion_promedio, 3), "\n\n")
  }
  
  # Confiabilidad
  if (!is.null(x$confiabilidad)) {
    cat("--- Confiabilidad ---\n")
    cat("Alpha de Cronbach:", round(x$confiabilidad$alpha_cronbach, 3), 
        "(", x$confiabilidad$interpretacion, ")\n")
    if (!is.null(x$confiabilidad$spearman_brown)) {
      cat("Spearman-Brown:", round(x$confiabilidad$spearman_brown, 3), "\n")
    }
    cat("\n")
  }
  
  # Recomendaciones
  if (!is.null(x$recomendaciones) && length(x$recomendaciones) > 0) {
    cat("--- Recomendaciones ---\n")
    for (i in seq_along(x$recomendaciones)) {
      cat("•", x$recomendaciones[[i]], "\n")
    }
  }
  
  invisible(x)
}

#' Método print para exportación LMS
#' @param x Objeto de clase exportacion_lms_icfes
#' @param ... Argumentos adicionales
#' @export
print.exportacion_lms_icfes <- function(x, ...) {
  cat("=== Exportación LMS ICFESMathExams ===\n")
  
  if (!is.null(x$metadatos)) {
    cat("Formato LMS:", x$metadatos$formato_lms, "\n")
    cat("Ejercicios exportados:", x$metadatos$num_ejercicios, "\n")
    cat("Fecha de exportación:", as.character(x$metadatos$fecha_exportacion), "\n")
    cat("Directorio de salida:", x$metadatos$directorio_salida, "\n\n")
  }
  
  cat("--- Archivos Generados ---\n")
  if (!is.null(x$archivo_principal)) {
    cat("Archivo principal:", x$archivo_principal, "\n")
  }
  
  if (!is.null(x$archivo_configuracion)) {
    cat("Configuración:", x$archivo_configuracion, "\n")
  }
  
  if (!is.null(x$archivos_items)) {
    cat("Archivos de ítems:", length(x$archivos_items), "archivos\n")
  }
  
  if (!is.null(x$formato)) {
    cat("Formato:", x$formato, "\n")
  }
  
  if (!is.null(x$instrucciones)) {
    cat("\n--- Instrucciones ---\n")
    cat(x$instrucciones, "\n")
  }
  
  invisible(x)
}

#' Método print para recomendaciones
#' @param x Objeto de clase recomendaciones_icfes
#' @param ... Argumentos adicionales
#' @export
print.recomendaciones_icfes <- function(x, ...) {
  cat("=== Recomendaciones Inteligentes ICFESMathExams ===\n")
  cat("Fecha de generación:", as.character(x$fecha_generacion), "\n")
  cat("Validez:", x$validez_dias, "días\n\n")
  
  if (!is.null(x$areas_prioritarias)) {
    cat("--- Áreas Prioritarias ---\n")
    for (area in x$areas_prioritarias) {
      cat("•", area, "\n")
    }
    cat("\n")
  }
  
  if (!is.null(x$ejercicios_recomendados)) {
    cat("--- Ejercicios Recomendados ---\n")
    cat("Número de ejercicios sugeridos:", length(x$ejercicios_recomendados), "\n\n")
  }
  
  if (!is.null(x$estrategias_estudio)) {
    cat("--- Estrategias de Estudio ---\n")
    for (estrategia in x$estrategias_estudio) {
      cat("•", estrategia, "\n")
    }
    cat("\n")
  }
  
  if (!is.null(x$cronograma_sugerido)) {
    cat("--- Cronograma Sugerido ---\n")
    cat("Plan de estudio personalizado disponible\n\n")
  }
  
  invisible(x)
}

#' Método print para reporte de compatibilidad LMS
#' @param x Objeto de clase reporte_compatibilidad_lms
#' @param ... Argumentos adicionales
#' @export
print.reporte_compatibilidad_lms <- function(x, ...) {
  cat("=== Reporte de Compatibilidad LMS ===\n")
  
  if (!is.null(x$resumen_general)) {
    cat("Ejercicios analizados:", x$resumen_general$num_ejercicios, "\n")
    cat("LMS evaluados:", paste(x$resumen_general$lms_analizados, collapse = ", "), "\n")
    cat("Fecha de análisis:", as.character(x$resumen_general$fecha_analisis), "\n\n")
  }
  
  if (!is.null(x$compatibilidad_por_lms)) {
    cat("--- Compatibilidad por LMS ---\n")
    for (lms in names(x$compatibilidad_por_lms)) {
      compat <- x$compatibilidad_por_lms[[lms]]
      cat(sprintf("%s: %s\n", lms, if(compat$compatible) "✓ Compatible" else "⚠ Problemas"))
    }
    cat("\n")
  }
  
  if (!is.null(x$ejercicios_problematicos) && length(x$ejercicios_problematicos) > 0) {
    cat("--- Ejercicios Problemáticos ---\n")
    for (lms in names(x$ejercicios_problematicos)) {
      problemas <- x$ejercicios_problematicos[[lms]]
      if (length(problemas) > 0) {
        cat(sprintf("%s: %d ejercicios requieren revisión\n", lms, length(problemas)))
      }
    }
    cat("\n")
  }
  
  if (!is.null(x$recomendaciones)) {
    cat("--- Recomendaciones ---\n")
    for (rec in x$recomendaciones) {
      cat("•", rec, "\n")
    }
  }
  
  invisible(x)
}

#' Método summary para examen adaptativo
#' @param object Objeto de clase examen_adaptativo_icfes
#' @param ... Argumentos adicionales
#' @export
summary.examen_adaptativo_icfes <- function(object, ...) {
  cat("=== Resumen de Examen Adaptativo ===\n")
  
  # Estadísticas básicas
  dificultades <- object$secuencia_dificultad
  cat("Estadísticas de dificultad:\n")
  cat("  Mínima:", min(dificultades, na.rm = TRUE), "\n")
  cat("  Máxima:", max(dificultades, na.rm = TRUE), "\n")
  cat("  Promedio:", round(mean(dificultades, na.rm = TRUE), 2), "\n")
  cat("  Desviación estándar:", round(sd(dificultades, na.rm = TRUE), 2), "\n\n")
  
  # Distribución de tipos
  tipos <- sapply(object$ejercicios_seleccionados, function(x) x$tipo %||% "Sin tipo")
  tabla_tipos <- table(tipos)
  cat("Distribución de tipos de ejercicios:\n")
  for (i in seq_along(tabla_tipos)) {
    cat(sprintf("  %s: %d\n", names(tabla_tipos)[i], tabla_tipos[i]))
  }
  
  invisible(object)
}

#' Método summary para análisis psicométrico
#' @param object Objeto de clase psicometria_icfes
#' @param ... Argumentos adicionales
#' @export
summary.psicometria_icfes <- function(object, ...) {
  cat("=== Resumen de Análisis Psicométrico ===\n\n")
  
  # Información general
  cat("Información General:\n")
  cat("  Estudiantes:", object$resumen_general$n_estudiantes, "\n")
  cat("  Ítems:", object$resumen_general$n_items, "\n")
  cat("  Modelo IRT:", object$resumen_general$modelo_irt, "\n\n")
  
  # Estadísticas de ítems
  if (!is.null(object$analisis_clasico$estadisticos)) {
    stats <- object$analisis_clasico$estadisticos
    cat("Estadísticas de Ítems:\n")
    cat("  Dificultad promedio:", round(mean(stats$dificultad, na.rm = TRUE), 3), "\n")
    cat("  Discriminación promedio:", round(mean(stats$discriminacion, na.rm = TRUE), 3), "\n")
    
    # Distribución de calidad
    tabla_calidad <- table(stats$calidad)
    cat("  Distribución de calidad:\n")
    for (i in seq_along(tabla_calidad)) {
      cat(sprintf("    %s: %d ítems\n", names(tabla_calidad)[i], tabla_calidad[i]))
    }
  }
  
  cat("\n")
  
  # Confiabilidad
  if (!is.null(object$confiabilidad)) {
    cat("Confiabilidad:\n")
    cat("  Alpha de Cronbach:", round(object$confiabilidad$alpha_cronbach, 3), "\n")
    cat("  Interpretación:", object$confiabilidad$interpretacion, "\n")
  }
  
  invisible(object)
}

# Operador %||% para valores por defecto
`%||%` <- function(x, y) if (is.null(x)) y else x
