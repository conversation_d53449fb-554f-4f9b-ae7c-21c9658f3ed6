#' @title Análisis Psicométrico Avanzado para ICFESMathExams
#' @description Funciones para análisis psicométrico, teoría de respuesta al ítem (IRT),
#' análisis de dificultad, discriminación y adaptabilidad de ejercicios.

#' Análisis psicométrico completo de ejercicios
#' 
#' Realiza análisis psicométrico completo incluyendo IRT, dificultad, discriminación
#' 
#' @param respuestas Matriz de respuestas (estudiantes x ítems)
#' @param ejercicios Lista de ejercicios evaluados
#' @param modelo_irt Modelo IRT a usar ("1PL", "2PL", "3PL")
#' @return Lista con resultados del análisis psicométrico
#' @export
#' @examples
#' \dontrun{
#' respuestas <- matrix(sample(0:1, 500, replace = TRUE), 50, 10)
#' analisis <- analizar_psicometria_completa(respuestas, ejercicios, "2PL")
#' }
analizar_psicometria_completa <- function(respuestas, ejercicios, modelo_irt = "2PL") {
  
  # Validar entrada
  if (!is.matrix(respuestas)) {
    stop("Las respuestas deben ser una matriz")
  }
  
  if (ncol(respuestas) != length(ejercicios)) {
    stop("El número de columnas debe coincidir con el número de ejercicios")
  }
  
  # Análisis clásico de ítems
  analisis_clasico <- analizar_items_clasico(respuestas)
  
  # Análisis IRT
  analisis_irt <- analizar_irt(respuestas, modelo_irt)
  
  # Análisis de confiabilidad
  confiabilidad <- calcular_confiabilidad(respuestas)
  
  # Análisis de validez
  validez <- analizar_validez_contenido(ejercicios)
  
  # Recomendaciones de mejora
  recomendaciones <- generar_recomendaciones_psicometricas(
    analisis_clasico, analisis_irt, ejercicios
  )
  
  # Compilar resultados
  resultado <- list(
    resumen_general = list(
      n_estudiantes = nrow(respuestas),
      n_items = ncol(respuestas),
      modelo_irt = modelo_irt,
      fecha_analisis = Sys.time()
    ),
    analisis_clasico = analisis_clasico,
    analisis_irt = analisis_irt,
    confiabilidad = confiabilidad,
    validez = validez,
    recomendaciones = recomendaciones,
    ejercicios_analizados = ejercicios
  )
  
  class(resultado) <- "psicometria_icfes"
  return(resultado)
}

#' Análisis clásico de ítems
#' 
#' @param respuestas Matriz de respuestas
#' @return Lista con estadísticos clásicos
#' @keywords internal
analizar_items_clasico <- function(respuestas) {
  
  n_items <- ncol(respuestas)
  n_estudiantes <- nrow(respuestas)
  
  # Calcular estadísticos para cada ítem
  estadisticos <- data.frame(
    item = 1:n_items,
    dificultad = colMeans(respuestas, na.rm = TRUE),
    discriminacion = numeric(n_items),
    correlacion_total = numeric(n_items),
    alpha_sin_item = numeric(n_items)
  )
  
  # Calcular discriminación (correlación punto-biserial)
  puntaje_total <- rowSums(respuestas, na.rm = TRUE)
  
  for (i in 1:n_items) {
    # Discriminación
    estadisticos$discriminacion[i] <- cor(respuestas[, i], puntaje_total, 
                                         use = "complete.obs")
    
    # Correlación ítem-total corregida
    puntaje_sin_item <- puntaje_total - respuestas[, i]
    estadisticos$correlacion_total[i] <- cor(respuestas[, i], puntaje_sin_item, 
                                           use = "complete.obs")
    
    # Alpha de Cronbach sin el ítem
    respuestas_sin_item <- respuestas[, -i, drop = FALSE]
    estadisticos$alpha_sin_item[i] <- calcular_alpha_cronbach(respuestas_sin_item)
  }
  
  # Clasificar ítems por calidad
  estadisticos$calidad <- clasificar_calidad_item(
    estadisticos$dificultad, 
    estadisticos$discriminacion
  )
  
  return(list(
    estadisticos = estadisticos,
    alpha_total = calcular_alpha_cronbach(respuestas),
    dificultad_promedio = mean(estadisticos$dificultad),
    discriminacion_promedio = mean(estadisticos$discriminacion, na.rm = TRUE)
  ))
}

#' Análisis de Teoría de Respuesta al Ítem (IRT)
#' 
#' @param respuestas Matriz de respuestas
#' @param modelo Modelo IRT ("1PL", "2PL", "3PL")
#' @return Lista con resultados IRT
#' @keywords internal
analizar_irt <- function(respuestas, modelo = "2PL") {
  
  # Verificar disponibilidad de paquetes IRT
  if (!requireNamespace("mirt", quietly = TRUE)) {
    warning("Paquete 'mirt' no disponible. Usando análisis simplificado.")
    return(analisis_irt_simplificado(respuestas))
  }
  
  tryCatch({
    # Ajustar modelo IRT
    if (modelo == "1PL") {
      modelo_irt <- mirt::mirt(respuestas, 1, itemtype = "Rasch", verbose = FALSE)
    } else if (modelo == "2PL") {
      modelo_irt <- mirt::mirt(respuestas, 1, itemtype = "2PL", verbose = FALSE)
    } else if (modelo == "3PL") {
      modelo_irt <- mirt::mirt(respuestas, 1, itemtype = "3PL", verbose = FALSE)
    } else {
      stop("Modelo IRT no válido. Use '1PL', '2PL' o '3PL'")
    }
    
    # Extraer parámetros de ítems
    parametros <- mirt::coef(modelo_irt, simplify = TRUE)$items
    
    # Calcular habilidades de estudiantes
    habilidades <- mirt::fscores(modelo_irt, method = "EAP")
    
    # Información del test
    info_test <- mirt::testinfo(modelo_irt, mirt::fscores(modelo_irt))
    
    # Ajuste del modelo
    ajuste <- mirt::M2(modelo_irt)
    
    return(list(
      modelo = modelo,
      parametros_items = parametros,
      habilidades_estudiantes = habilidades,
      informacion_test = info_test,
      ajuste_modelo = ajuste,
      modelo_ajustado = modelo_irt
    ))
    
  }, error = function(e) {
    warning("Error en análisis IRT: ", e$message, ". Usando análisis simplificado.")
    return(analisis_irt_simplificado(respuestas))
  })
}

#' Análisis IRT simplificado cuando mirt no está disponible
#' 
#' @param respuestas Matriz de respuestas
#' @return Lista con análisis simplificado
#' @keywords internal
analisis_irt_simplificado <- function(respuestas) {
  
  # Estimaciones simplificadas de parámetros IRT
  dificultad <- qlogis(colMeans(respuestas, na.rm = TRUE))
  
  # Discriminación aproximada usando correlación ítem-total
  puntaje_total <- rowSums(respuestas, na.rm = TRUE)
  discriminacion <- sapply(1:ncol(respuestas), function(i) {
    cor(respuestas[, i], puntaje_total, use = "complete.obs")
  })
  
  # Habilidades aproximadas
  habilidades <- scale(puntaje_total)[, 1]
  
  return(list(
    modelo = "Simplificado",
    parametros_items = data.frame(
      item = 1:ncol(respuestas),
      dificultad = dificultad,
      discriminacion = discriminacion
    ),
    habilidades_estudiantes = habilidades,
    nota = "Análisis simplificado. Instale 'mirt' para análisis completo."
  ))
}

#' Calcular Alpha de Cronbach
#' 
#' @param respuestas Matriz de respuestas
#' @return Valor de Alpha de Cronbach
#' @keywords internal
calcular_alpha_cronbach <- function(respuestas) {
  
  if (ncol(respuestas) < 2) return(NA)
  
  # Varianzas de ítems
  var_items <- apply(respuestas, 2, var, na.rm = TRUE)
  
  # Varianza total
  var_total <- var(rowSums(respuestas, na.rm = TRUE), na.rm = TRUE)
  
  # Alpha de Cronbach
  k <- ncol(respuestas)
  alpha <- (k / (k - 1)) * (1 - sum(var_items, na.rm = TRUE) / var_total)
  
  return(alpha)
}

#' Clasificar calidad de ítems
#' 
#' @param dificultad Vector de dificultades
#' @param discriminacion Vector de discriminaciones
#' @return Vector de clasificaciones
#' @keywords internal
clasificar_calidad_item <- function(dificultad, discriminacion) {
  
  calidad <- character(length(dificultad))
  
  for (i in seq_along(dificultad)) {
    d <- dificultad[i]
    disc <- discriminacion[i]
    
    if (is.na(d) || is.na(disc)) {
      calidad[i] <- "Sin datos"
    } else if (disc < 0.2) {
      calidad[i] <- "Pobre"
    } else if (disc < 0.3) {
      if (d >= 0.2 && d <= 0.8) {
        calidad[i] <- "Aceptable"
      } else {
        calidad[i] <- "Revisar"
      }
    } else if (disc >= 0.3) {
      if (d >= 0.2 && d <= 0.8) {
        calidad[i] <- "Bueno"
      } else {
        calidad[i] <- "Revisar dificultad"
      }
    } else {
      calidad[i] <- "Revisar"
    }
  }
  
  return(calidad)
}

#' Analizar validez de contenido
#' 
#' @param ejercicios Lista de ejercicios
#' @return Lista con análisis de validez
#' @keywords internal
analizar_validez_contenido <- function(ejercicios) {
  
  # Análisis de cobertura de competencias
  competencias <- sapply(ejercicios, function(x) x$competencia %||% "No especificada")
  cobertura_competencias <- table(competencias)
  
  # Análisis de niveles de dificultad
  dificultades <- sapply(ejercicios, function(x) x$nivel_dificultad %||% 3)
  distribucion_dificultad <- table(dificultades)
  
  # Análisis de tipos de ejercicios
  tipos <- sapply(ejercicios, function(x) x$tipo %||% "No especificado")
  distribucion_tipos <- table(tipos)
  
  # Análisis de contextos
  contextos <- sapply(ejercicios, function(x) {
    if (!is.null(x$contexto_icfes)) {
      if (grepl("Colombia|colombiano|ICFES", x$contexto_icfes, ignore.case = TRUE)) {
        return("Contextualizado")
      }
    }
    return("Sin contexto")
  })
  contextualizacion <- table(contextos)
  
  return(list(
    cobertura_competencias = cobertura_competencias,
    distribucion_dificultad = distribucion_dificultad,
    distribucion_tipos = distribucion_tipos,
    contextualizacion = contextualizacion,
    balance_general = evaluar_balance_contenido(
      cobertura_competencias, 
      distribucion_dificultad, 
      contextualizacion
    )
  ))
}

#' Evaluar balance de contenido
#' 
#' @param competencias Tabla de competencias
#' @param dificultades Tabla de dificultades
#' @param contextos Tabla de contextos
#' @return Lista con evaluación de balance
#' @keywords internal
evaluar_balance_contenido <- function(competencias, dificultades, contextos) {
  
  # Evaluar balance de competencias
  balance_competencias <- if (length(competencias) >= 3) {
    coef_var <- sd(as.numeric(competencias)) / mean(as.numeric(competencias))
    if (coef_var < 0.5) "Balanceado" else "Desbalanceado"
  } else {
    "Insuficiente cobertura"
  }
  
  # Evaluar balance de dificultades
  balance_dificultades <- if (length(dificultades) >= 3) {
    "Adecuado"
  } else {
    "Rango limitado"
  }
  
  # Evaluar contextualización
  prop_contextualizado <- if ("Contextualizado" %in% names(contextos)) {
    contextos["Contextualizado"] / sum(contextos)
  } else {
    0
  }
  
  nivel_contextualizacion <- if (prop_contextualizado >= 0.7) {
    "Alto"
  } else if (prop_contextualizado >= 0.4) {
    "Medio"
  } else {
    "Bajo"
  }
  
  return(list(
    balance_competencias = balance_competencias,
    balance_dificultades = balance_dificultades,
    nivel_contextualizacion = nivel_contextualizacion,
    proporcion_contextualizado = prop_contextualizado
  ))
}

#' Generar recomendaciones psicométricas
#' 
#' @param analisis_clasico Resultados del análisis clásico
#' @param analisis_irt Resultados del análisis IRT
#' @param ejercicios Lista de ejercicios
#' @return Lista con recomendaciones
#' @keywords internal
generar_recomendaciones_psicometricas <- function(analisis_clasico, analisis_irt, ejercicios) {
  
  recomendaciones <- list()
  
  # Recomendaciones basadas en confiabilidad
  if (analisis_clasico$alpha_total < 0.7) {
    recomendaciones$confiabilidad <- "Mejorar confiabilidad: Alpha < 0.7. Revisar ítems con baja correlación."
  } else if (analisis_clasico$alpha_total < 0.8) {
    recomendaciones$confiabilidad <- "Confiabilidad aceptable. Considerar mejoras menores."
  } else {
    recomendaciones$confiabilidad <- "Excelente confiabilidad."
  }
  
  # Recomendaciones por ítem
  items_problematicos <- which(analisis_clasico$estadisticos$calidad %in% c("Pobre", "Revisar"))
  
  if (length(items_problematicos) > 0) {
    recomendaciones$items_revisar <- sprintf(
      "Revisar ítems: %s. Problemas de discriminación o dificultad.",
      paste(items_problematicos, collapse = ", ")
    )
  }
  
  # Recomendaciones de dificultad
  dif_promedio <- analisis_clasico$dificultad_promedio
  if (dif_promedio < 0.3) {
    recomendaciones$dificultad <- "Test muy difícil. Considerar ítems más fáciles."
  } else if (dif_promedio > 0.8) {
    recomendaciones$dificultad <- "Test muy fácil. Considerar ítems más difíciles."
  }
  
  # Recomendaciones de discriminación
  disc_promedio <- analisis_clasico$discriminacion_promedio
  if (disc_promedio < 0.3) {
    recomendaciones$discriminacion <- "Baja discriminación promedio. Revisar calidad de ítems."
  }
  
  return(recomendaciones)
}

#' Calcular confiabilidad del test
#' 
#' @param respuestas Matriz de respuestas
#' @return Lista con medidas de confiabilidad
#' @keywords internal
calcular_confiabilidad <- function(respuestas) {
  
  alpha <- calcular_alpha_cronbach(respuestas)
  
  # Confiabilidad split-half
  n_items <- ncol(respuestas)
  mitad1 <- respuestas[, 1:(n_items %/% 2)]
  mitad2 <- respuestas[, (n_items %/% 2 + 1):n_items]
  
  puntaje1 <- rowSums(mitad1, na.rm = TRUE)
  puntaje2 <- rowSums(mitad2, na.rm = TRUE)
  
  correlacion_mitades <- cor(puntaje1, puntaje2, use = "complete.obs")
  spearman_brown <- (2 * correlacion_mitades) / (1 + correlacion_mitades)
  
  return(list(
    alpha_cronbach = alpha,
    split_half = correlacion_mitades,
    spearman_brown = spearman_brown,
    interpretacion = interpretar_confiabilidad(alpha)
  ))
}

#' Interpretar nivel de confiabilidad
#' 
#' @param alpha Valor de Alpha de Cronbach
#' @return String con interpretación
#' @keywords internal
interpretar_confiabilidad <- function(alpha) {
  if (is.na(alpha)) return("No calculable")
  if (alpha >= 0.9) return("Excelente")
  if (alpha >= 0.8) return("Buena")
  if (alpha >= 0.7) return("Aceptable")
  if (alpha >= 0.6) return("Cuestionable")
  return("Pobre")
}
