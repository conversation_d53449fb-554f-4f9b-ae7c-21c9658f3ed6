#' @title Sistema de Adaptabilidad y Personalización ICFESMathExams
#' @description Funciones para crear exámenes adaptativos, personalización
#' por nivel de estudiante y recomendaciones inteligentes.

#' Generar examen adaptativo
#' 
#' Crea un examen que se adapta al nivel del estudiante basado en respuestas previas
#' 
#' @param perfil_estudiante Lista con información del estudiante
#' @param banco_ejercicios Lista de ejercicios disponibles
#' @param num_ejercicios Número de ejercicios en el examen
#' @param algoritmo_adaptacion Algoritmo a usar ("irt_adaptivo", "dificultad_progresiva", "competencias_balanceadas")
#' @return Lista con examen adaptativo generado
#' @export
#' @examples
#' \dontrun{
#' perfil <- list(habilidad_estimada = 0.5, historial_respuestas = c(1,0,1,1,0))
#' examen <- generar_examen_adaptativo(perfil, banco_ejercicios, 10)
#' }
generar_examen_adaptativo <- function(perfil_estudiante, banco_ejercicios, 
                                     num_ejercicios = 10, 
                                     algoritmo_adaptacion = "irt_adaptivo") {
  
  # Validar entrada
  if (length(banco_ejercicios) < num_ejercicios) {
    stop("El banco de ejercicios debe tener al menos ", num_ejercicios, " ejercicios")
  }
  
  # Inicializar examen adaptativo
  examen_adaptativo <- list(
    ejercicios_seleccionados = list(),
    secuencia_dificultad = numeric(),
    justificacion_seleccion = character(),
    perfil_inicial = perfil_estudiante,
    algoritmo_usado = algoritmo_adaptacion
  )
  
  # Aplicar algoritmo de adaptación
  examen_adaptativo <- switch(algoritmo_adaptacion,
    "irt_adaptivo" = aplicar_irt_adaptivo(examen_adaptativo, perfil_estudiante, 
                                         banco_ejercicios, num_ejercicios),
    "dificultad_progresiva" = aplicar_dificultad_progresiva(examen_adaptativo, 
                                                           perfil_estudiante, 
                                                           banco_ejercicios, num_ejercicios),
    "competencias_balanceadas" = aplicar_competencias_balanceadas(examen_adaptativo, 
                                                                 perfil_estudiante, 
                                                                 banco_ejercicios, num_ejercicios),
    stop("Algoritmo de adaptación no válido: ", algoritmo_adaptacion)
  )
  
  # Agregar metadatos del examen
  examen_adaptativo$metadatos <- list(
    fecha_generacion = Sys.time(),
    num_ejercicios = length(examen_adaptativo$ejercicios_seleccionados),
    dificultad_promedio = mean(examen_adaptativo$secuencia_dificultad),
    rango_dificultad = range(examen_adaptativo$secuencia_dificultad),
    competencias_cubiertas = obtener_competencias_cubiertas(examen_adaptativo$ejercicios_seleccionados)
  )
  
  class(examen_adaptativo) <- "examen_adaptativo_icfes"
  return(examen_adaptativo)
}

#' Aplicar algoritmo IRT adaptativo
#' 
#' @param examen Lista del examen en construcción
#' @param perfil Perfil del estudiante
#' @param banco Banco de ejercicios
#' @param num_ejercicios Número de ejercicios
#' @return Examen con ejercicios seleccionados
#' @keywords internal
aplicar_irt_adaptivo <- function(examen, perfil, banco, num_ejercicios) {
  
  # Estimar habilidad inicial
  habilidad_actual <- perfil$habilidad_estimada %||% 0
  
  # Seleccionar ejercicios adaptativamente
  ejercicios_usados <- integer()
  
  for (i in 1:num_ejercicios) {
    # Calcular información de cada ejercicio no usado
    ejercicios_disponibles <- setdiff(1:length(banco), ejercicios_usados)
    
    if (length(ejercicios_disponibles) == 0) {
      warning("Se agotaron los ejercicios disponibles")
      break
    }
    
    # Seleccionar ejercicio con máxima información
    mejor_ejercicio <- seleccionar_ejercicio_max_informacion(
      banco[ejercicios_disponibles], 
      habilidad_actual
    )
    
    indice_ejercicio <- ejercicios_disponibles[mejor_ejercicio]
    ejercicio_seleccionado <- banco[[indice_ejercicio]]
    
    # Agregar al examen
    examen$ejercicios_seleccionados[[i]] <- ejercicio_seleccionado
    # Convertir nivel de dificultad a numérico
    nivel_dificultad <- convertir_dificultad_numerica(ejercicio_seleccionado$nivel_dificultad %||% "intermedio")
    examen$secuencia_dificultad[i] <- nivel_dificultad
    examen$justificacion_seleccion[i] <- sprintf(
      "Ejercicio %d: Máxima información para habilidad %.2f",
      indice_ejercicio, habilidad_actual
    )
    
    ejercicios_usados <- c(ejercicios_usados, indice_ejercicio)
    
    # Simular respuesta y actualizar habilidad (para próxima selección)
    # En implementación real, esto vendría de la respuesta del estudiante
    prob_correcta <- calcular_probabilidad_respuesta(ejercicio_seleccionado, habilidad_actual)
    respuesta_simulada <- rbinom(1, 1, prob_correcta)
    habilidad_actual <- actualizar_habilidad_irt(habilidad_actual, 
                                                 ejercicio_seleccionado, 
                                                 respuesta_simulada)
  }
  
  return(examen)
}

#' Aplicar algoritmo de dificultad progresiva
#' 
#' @param examen Lista del examen en construcción
#' @param perfil Perfil del estudiante
#' @param banco Banco de ejercicios
#' @param num_ejercicios Número de ejercicios
#' @return Examen con ejercicios seleccionados
#' @keywords internal
aplicar_dificultad_progresiva <- function(examen, perfil, banco, num_ejercicios) {
  
  # Determinar nivel inicial basado en historial
  nivel_inicial <- determinar_nivel_inicial(perfil)
  
  # Crear secuencia de dificultad progresiva
  secuencia_objetivo <- crear_secuencia_progresiva(nivel_inicial, num_ejercicios)
  
  # Seleccionar ejercicios que coincidan con la secuencia
  for (i in 1:num_ejercicios) {
    dificultad_objetivo <- secuencia_objetivo[i]
    
    # Buscar ejercicios con dificultad cercana
    ejercicio_seleccionado <- seleccionar_por_dificultad(banco, dificultad_objetivo)
    
    examen$ejercicios_seleccionados[[i]] <- ejercicio_seleccionado
    # Convertir nivel de dificultad a numérico
    nivel_dificultad <- convertir_dificultad_numerica(ejercicio_seleccionado$nivel_dificultad %||% dificultad_objetivo)
    examen$secuencia_dificultad[i] <- nivel_dificultad
    examen$justificacion_seleccion[i] <- sprintf(
      "Ejercicio con dificultad %.1f (objetivo: %.1f)",
      nivel_dificultad,
      dificultad_objetivo
    )
    
    # Remover ejercicio del banco para evitar repetición
    banco <- banco[!sapply(banco, function(x) identical(x, ejercicio_seleccionado))]
  }
  
  return(examen)
}

#' Aplicar algoritmo de competencias balanceadas
#' 
#' @param examen Lista del examen en construcción
#' @param perfil Perfil del estudiante
#' @param banco Banco de ejercicios
#' @param num_ejercicios Número de ejercicios
#' @return Examen con ejercicios seleccionados
#' @keywords internal
aplicar_competencias_balanceadas <- function(examen, perfil, banco, num_ejercicios) {
  
  # Identificar competencias disponibles
  competencias_disponibles <- unique(sapply(banco, function(x) x$competencia %||% "General"))
  
  # Distribuir ejercicios entre competencias
  distribucion <- distribuir_ejercicios_competencias(competencias_disponibles, num_ejercicios)
  
  ejercicios_seleccionados <- list()
  contador <- 1
  
  for (competencia in names(distribucion)) {
    num_ejercicios_competencia <- distribucion[[competencia]]
    
    # Filtrar ejercicios de esta competencia
    ejercicios_competencia <- banco[sapply(banco, function(x) {
      (x$competencia %||% "General") == competencia
    })]
    
    if (length(ejercicios_competencia) == 0) {
      warning("No hay ejercicios disponibles para competencia: ", competencia)
      next
    }
    
    # Seleccionar ejercicios de esta competencia
    for (j in 1:min(num_ejercicios_competencia, length(ejercicios_competencia))) {
      if (contador > num_ejercicios) break
      
      # Seleccionar ejercicio apropiado para el nivel del estudiante
      ejercicio <- seleccionar_ejercicio_competencia(ejercicios_competencia, perfil)
      
      examen$ejercicios_seleccionados[[contador]] <- ejercicio
      # Convertir nivel de dificultad a numérico
      nivel_dificultad <- convertir_dificultad_numerica(ejercicio$nivel_dificultad %||% "intermedio")
      examen$secuencia_dificultad[contador] <- nivel_dificultad
      examen$justificacion_seleccion[contador] <- sprintf(
        "Competencia: %s (ejercicio %d de %d)",
        competencia, j, num_ejercicios_competencia
      )
      
      # Remover ejercicio para evitar repetición
      ejercicios_competencia <- ejercicios_competencia[!sapply(ejercicios_competencia, 
                                                              function(x) identical(x, ejercicio))]
      contador <- contador + 1
    }
  }
  
  return(examen)
}

#' Personalizar examen por perfil de estudiante
#' 
#' Crea un examen personalizado basado en el perfil completo del estudiante
#' 
#' @param perfil_estudiante Lista detallada con información del estudiante
#' @param banco_ejercicios Lista de ejercicios disponibles
#' @param configuracion_examen Lista con configuración del examen
#' @return Lista con examen personalizado
#' @export
#' @examples
#' \dontrun{
#' perfil <- list(grado = 11, fortalezas = c("algebra"), debilidades = c("geometria"))
#' examen <- personalizar_examen_estudiante(perfil, banco_ejercicios)
#' }
personalizar_examen_estudiante <- function(perfil_estudiante, banco_ejercicios, 
                                          configuracion_examen = list()) {
  
  # Configuración por defecto
  config_default <- list(
    num_ejercicios = 15,
    enfoque_debilidades = 0.6,  # 60% en debilidades, 40% en fortalezas
    incluir_repaso = TRUE,
    dificultad_adaptativa = TRUE,
    contexto_personalizado = TRUE
  )
  
  config <- modifyList(config_default, configuracion_examen)
  
  # Analizar perfil del estudiante
  analisis_perfil <- analizar_perfil_estudiante(perfil_estudiante)
  
  # Filtrar ejercicios relevantes
  ejercicios_relevantes <- filtrar_ejercicios_perfil(banco_ejercicios, analisis_perfil)
  
  # Distribuir ejercicios según fortalezas/debilidades
  distribucion <- calcular_distribucion_personalizada(analisis_perfil, config)
  
  # Seleccionar ejercicios
  ejercicios_seleccionados <- seleccionar_ejercicios_personalizados(
    ejercicios_relevantes, distribucion, analisis_perfil
  )
  
  # Crear examen personalizado
  examen_personalizado <- list(
    ejercicios = ejercicios_seleccionados,
    perfil_estudiante = perfil_estudiante,
    analisis_perfil = analisis_perfil,
    configuracion = config,
    recomendaciones_estudio = generar_recomendaciones_estudio(analisis_perfil),
    plan_mejora = generar_plan_mejora(analisis_perfil, ejercicios_seleccionados),
    metadatos = list(
      fecha_creacion = Sys.time(),
      num_ejercicios = length(ejercicios_seleccionados),
      enfoque_principal = analisis_perfil$enfoque_recomendado
    )
  )
  
  class(examen_personalizado) <- "examen_personalizado_icfes"
  return(examen_personalizado)
}

#' Generar recomendaciones inteligentes
#' 
#' Crea recomendaciones de estudio basadas en análisis de rendimiento
#' 
#' @param historial_respuestas Matriz con historial de respuestas del estudiante
#' @param ejercicios_realizados Lista de ejercicios realizados
#' @param objetivos_aprendizaje Lista con objetivos del estudiante
#' @return Lista con recomendaciones inteligentes
#' @export
#' @examples
#' \dontrun{
#' recomendaciones <- generar_recomendaciones_inteligentes(historial, ejercicios, objetivos)
#' }
generar_recomendaciones_inteligentes <- function(historial_respuestas, 
                                               ejercicios_realizados, 
                                               objetivos_aprendizaje = list()) {
  
  # Analizar patrones de rendimiento
  patrones <- analizar_patrones_rendimiento(historial_respuestas, ejercicios_realizados)
  
  # Identificar áreas de mejora
  areas_mejora <- identificar_areas_mejora(patrones)
  
  # Generar recomendaciones específicas
  recomendaciones <- list(
    areas_prioritarias = areas_mejora$prioritarias,
    ejercicios_recomendados = recomendar_ejercicios_especificos(areas_mejora, ejercicios_realizados),
    estrategias_estudio = generar_estrategias_estudio(patrones),
    cronograma_sugerido = crear_cronograma_estudio(areas_mejora, objetivos_aprendizaje),
    recursos_adicionales = sugerir_recursos_adicionales(areas_mejora)
  )
  
  # Personalizar según objetivos
  if (length(objetivos_aprendizaje) > 0) {
    recomendaciones <- personalizar_por_objetivos(recomendaciones, objetivos_aprendizaje)
  }
  
  recomendaciones$fecha_generacion <- Sys.time()
  recomendaciones$validez_dias <- 30  # Recomendaciones válidas por 30 días
  
  class(recomendaciones) <- "recomendaciones_icfes"
  return(recomendaciones)
}

#' Funciones auxiliares para adaptabilidad
#' 
#' @keywords internal

# Seleccionar ejercicio con máxima información
seleccionar_ejercicio_max_informacion <- function(ejercicios, habilidad) {
  informaciones <- sapply(ejercicios, function(ej) {
    calcular_informacion_ejercicio(ej, habilidad)
  })
  return(which.max(informaciones))
}

# Calcular información de un ejercicio
calcular_informacion_ejercicio <- function(ejercicio, habilidad) {
  # Implementación simplificada de información de Fisher
  dificultad <- ejercicio$nivel_dificultad %||% 3
  discriminacion <- ejercicio$discriminacion %||% 1
  
  # Convertir a escala IRT
  dif_irt <- (dificultad - 3) / 2  # Escala -1 a 1
  
  # Calcular probabilidad
  prob <- 1 / (1 + exp(-discriminacion * (habilidad - dif_irt)))
  
  # Información de Fisher
  informacion <- discriminacion^2 * prob * (1 - prob)
  return(informacion)
}

# Calcular probabilidad de respuesta correcta
calcular_probabilidad_respuesta <- function(ejercicio, habilidad) {
  dificultad <- ejercicio$nivel_dificultad %||% 3
  discriminacion <- ejercicio$discriminacion %||% 1
  
  dif_irt <- (dificultad - 3) / 2
  prob <- 1 / (1 + exp(-discriminacion * (habilidad - dif_irt)))
  return(prob)
}

# Actualizar habilidad usando IRT
actualizar_habilidad_irt <- function(habilidad_actual, ejercicio, respuesta) {
  # Implementación simplificada de actualización bayesiana
  dificultad <- ejercicio$nivel_dificultad %||% 3
  discriminacion <- ejercicio$discriminacion %||% 1
  
  dif_irt <- (dificultad - 3) / 2
  
  # Factor de actualización basado en respuesta
  if (respuesta == 1) {
    # Respuesta correcta: aumentar habilidad si ejercicio era difícil
    ajuste <- 0.1 * (dif_irt - habilidad_actual)
  } else {
    # Respuesta incorrecta: disminuir habilidad si ejercicio era fácil
    ajuste <- -0.1 * (habilidad_actual - dif_irt)
  }
  
  nueva_habilidad <- habilidad_actual + ajuste
  return(max(-3, min(3, nueva_habilidad)))  # Limitar entre -3 y 3
}

#' Distribuir ejercicios entre competencias
#'
#' @param competencias Vector de competencias disponibles
#' @param num_ejercicios Número total de ejercicios
#' @return Lista con distribución de ejercicios por competencia
#' @keywords internal
distribuir_ejercicios_competencias <- function(competencias, num_ejercicios) {

  # Distribución base equitativa
  ejercicios_por_competencia <- floor(num_ejercicios / length(competencias))
  ejercicios_restantes <- num_ejercicios %% length(competencias)

  # Crear distribución inicial
  distribucion <- rep(ejercicios_por_competencia, length(competencias))
  names(distribucion) <- competencias

  # Distribuir ejercicios restantes
  if (ejercicios_restantes > 0) {
    indices_extra <- sample(seq_along(competencias), ejercicios_restantes)
    distribucion[indices_extra] <- distribucion[indices_extra] + 1
  }

  # Asegurar que cada competencia tenga al menos 1 ejercicio si es posible
  if (num_ejercicios >= length(competencias)) {
    distribucion[distribucion == 0] <- 1
    # Reajustar si es necesario
    while (sum(distribucion) > num_ejercicios) {
      max_idx <- which.max(distribucion)
      distribucion[max_idx] <- distribucion[max_idx] - 1
    }
  }

  return(as.list(distribucion))
}

#' Seleccionar ejercicio apropiado para una competencia
#'
#' @param ejercicios_competencia Lista de ejercicios de la competencia
#' @param perfil Perfil del estudiante
#' @return Ejercicio seleccionado
#' @keywords internal
seleccionar_ejercicio_competencia <- function(ejercicios_competencia, perfil) {

  if (length(ejercicios_competencia) == 0) {
    stop("No hay ejercicios disponibles para la competencia")
  }

  # Si solo hay un ejercicio, seleccionarlo
  if (length(ejercicios_competencia) == 1) {
    return(ejercicios_competencia[[1]])
  }

  # Calcular puntuaciones para cada ejercicio basado en el perfil
  puntuaciones <- sapply(ejercicios_competencia, function(ejercicio) {
    puntuacion <- 0

    # Considerar habilidad estimada del estudiante
    habilidad <- perfil$habilidad_estimada %||% 0.5
    dificultad_ejercicio <- ejercicio$nivel_dificultad %||% "intermedio"

    # Convertir dificultad a numérico
    dif_num <- switch(as.character(dificultad_ejercicio),
                      "facil" = 1,
                      "intermedio" = 2,
                      "dificil" = 3,
                      2)  # por defecto

    # Preferir ejercicios apropiados para el nivel del estudiante
    nivel_apropiado <- round(habilidad * 3) + 1  # 1-4 basado en habilidad 0-1
    diferencia_nivel <- abs(dif_num - nivel_apropiado)
    puntuacion <- puntuacion + (3 - diferencia_nivel)  # Mayor puntuación para menor diferencia

    # Considerar fortalezas y debilidades
    tipo_ejercicio <- ejercicio$tipo %||% ""
    if (tipo_ejercicio %in% (perfil$fortalezas %||% c())) {
      puntuacion <- puntuacion + 1
    }
    if (tipo_ejercicio %in% (perfil$debilidades %||% c())) {
      puntuacion <- puntuacion + 2  # Dar más peso a las debilidades para reforzar
    }

    return(puntuacion)
  })

  # Seleccionar ejercicio con mayor puntuación (con algo de aleatoriedad)
  if (max(puntuaciones) > 0) {
    # Seleccionar entre los mejores ejercicios
    mejores_indices <- which(puntuaciones == max(puntuaciones))
    indice_seleccionado <- sample(mejores_indices, 1)
  } else {
    # Si no hay puntuaciones positivas, seleccionar aleatoriamente
    indice_seleccionado <- sample(seq_along(ejercicios_competencia), 1)
  }

  return(ejercicios_competencia[[indice_seleccionado]])
}

#' Obtener competencias cubiertas por los ejercicios seleccionados
#'
#' @param ejercicios_seleccionados Lista de ejercicios seleccionados
#' @return Vector de competencias únicas cubiertas
#' @keywords internal
obtener_competencias_cubiertas <- function(ejercicios_seleccionados) {

  if (length(ejercicios_seleccionados) == 0) {
    return(character(0))
  }

  # Extraer competencias de cada ejercicio
  competencias <- sapply(ejercicios_seleccionados, function(ejercicio) {
    ejercicio$competencia %||% "General"
  })

  # Devolver competencias únicas
  return(unique(competencias))
}

#' Convertir nivel de dificultad a valor numérico
#'
#' @param nivel_dificultad Nivel de dificultad como string o número
#' @return Valor numérico de dificultad (1-5)
#' @keywords internal
convertir_dificultad_numerica <- function(nivel_dificultad) {

  # Si ya es numérico, devolverlo
  if (is.numeric(nivel_dificultad)) {
    return(as.numeric(nivel_dificultad))
  }

  # Convertir string a numérico
  nivel_str <- as.character(nivel_dificultad)
  nivel_numerico <- switch(tolower(nivel_str),
    "muy_facil" = 1,
    "facil" = 2,
    "intermedio" = 3,
    "dificil" = 4,
    "muy_dificil" = 5,
    "avanzado" = 5,
    # Valores por defecto si no coincide
    3  # intermedio por defecto
  )

  return(nivel_numerico)
}

# Operador %||% para valores por defecto
`%||%` <- function(x, y) if (is.null(x)) y else x
