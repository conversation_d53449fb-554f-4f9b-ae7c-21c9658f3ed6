#' @title Configuración Principal del Paquete ICFESMathExams Optimizado
#' @description Funciones de configuración central, inicialización y gestión
#' de configuraciones globales del paquete optimizado.

#' Inicializar ICFESMathExams con configuración optimizada
#' 
#' Configura el paquete con todas las funcionalidades optimizadas
#' 
#' @param configuracion Lista con configuración personalizada
#' @param modo_operacion Modo de operación ("desarrollo", "produccion", "educativo")
#' @param habilitar_cache Habilitar caché para mejorar rendimiento
#' @return Lista con configuración aplicada
#' @export
#' @examples
#' \dontrun{
#' config <- inicializar_icfes_optimizado(modo_operacion = "educativo")
#' }
inicializar_icfes_optimizado <- function(configuracion = list(), 
                                        modo_operacion = "educativo",
                                        habilitar_cache = TRUE) {
  
  # Configuración por defecto optimizada
  config_default <- list(
    # Configuración general
    version_paquete = "3.0.0",
    modo_operacion = modo_operacion,
    idioma = "es",
    zona_horaria = "America/Bogota",
    
    # Configuración de generación
    generacion = list(
      tipos_ejercicios_disponibles = c(
        "movimiento_lineal", "estadistica_descriptiva", "probabilidad_basica",
        "variacion_exponencial", "geometria_analitica", "funciones_matematicas",
        "estadistica_avanzada", "algebra_avanzada", "interpretacion_graficas"
      ),
      max_versiones_simultaneas = 1000,
      semilla_aleatoria = NULL,
      validacion_automatica = TRUE,
      cache_ejercicios = habilitar_cache
    ),
    
    # Configuración r-exams
    rexams = list(
      formatos_exportacion = c("pdf", "html", "moodle", "canvas", "blackboard", "qti", "scorm"),
      directorio_templates = "templates/",
      directorio_salida = "output/",
      usar_latex = TRUE,
      usar_tikz = TRUE,
      compilador_latex = "pdflatex"
    ),
    
    # Configuración psicométrica
    psicometria = list(
      modelo_irt_default = "2PL",
      nivel_confianza = 0.95,
      criterio_dificultad_min = 0.2,
      criterio_dificultad_max = 0.8,
      criterio_discriminacion_min = 0.3,
      analisis_automatico = TRUE
    ),
    
    # Configuración de adaptabilidad
    adaptabilidad = list(
      algoritmo_default = "irt_adaptivo",
      num_ejercicios_default = 15,
      enfoque_debilidades = 0.6,
      actualizacion_habilidad = TRUE,
      personalizacion_contexto = TRUE
    ),
    
    # Configuración LMS
    lms = list(
      formatos_soportados = c("moodle", "canvas", "blackboard", "brightspace"),
      exportacion_automatica = FALSE,
      validacion_compatibilidad = TRUE,
      incluir_metadatos = TRUE
    ),
    
    # Configuración de validación
    validacion = list(
      validacion_estricta = TRUE,
      criterios_calidad = list(
        matematica = 0.8,
        pedagogica = 0.7,
        tecnica = 0.9,
        contextual = 0.6
      ),
      generar_reportes = TRUE
    ),
    
    # Configuración de rendimiento
    rendimiento = list(
      usar_paralelizacion = TRUE,
      num_cores = parallel::detectCores() - 1,
      cache_resultados = habilitar_cache,
      optimizar_memoria = TRUE,
      timeout_generacion = 300  # segundos
    )
  )
  
  # Combinar con configuración personalizada
  config_final <- modifyList(config_default, configuracion)
  
  # Aplicar configuración según modo de operación
  config_final <- aplicar_modo_operacion(config_final, modo_operacion)
  
  # Validar configuración
  validar_configuracion(config_final)
  
  # Establecer opciones globales
  establecer_opciones_globales(config_final)
  
  # Inicializar componentes
  inicializar_componentes(config_final)
  
  # Guardar configuración en el entorno del paquete
  assign("configuracion_icfes", config_final, envir = .ICFESMathExamsEnv)
  
  message("✅ ICFESMathExams v3.0.0 inicializado correctamente")
  message("📊 Modo: ", modo_operacion)
  message("🔧 Tipos de ejercicios disponibles: ", length(config_final$generacion$tipos_ejercicios_disponibles))
  message("📤 Formatos de exportación: ", length(config_final$rexams$formatos_exportacion))
  
  invisible(config_final)
}

#' Aplicar configuración según modo de operación
#' 
#' @param config Configuración base
#' @param modo Modo de operación
#' @return Configuración ajustada
#' @keywords internal
aplicar_modo_operacion <- function(config, modo) {
  
  switch(modo,
    "desarrollo" = {
      config$validacion$validacion_estricta <- FALSE
      config$rendimiento$usar_paralelizacion <- FALSE
      config$generacion$cache_ejercicios <- FALSE
      config$psicometria$analisis_automatico <- FALSE
      message("🔧 Modo desarrollo: Validaciones relajadas, sin caché")
    },
    "produccion" = {
      config$validacion$validacion_estricta <- TRUE
      config$rendimiento$usar_paralelizacion <- TRUE
      config$generacion$cache_ejercicios <- TRUE
      config$psicometria$analisis_automatico <- TRUE
      config$rendimiento$optimizar_memoria <- TRUE
      message("🚀 Modo producción: Máximo rendimiento y validaciones")
    },
    "educativo" = {
      config$validacion$validacion_estricta <- TRUE
      config$adaptabilidad$personalizacion_contexto <- TRUE
      config$lms$exportacion_automatica <- TRUE
      config$validacion$generar_reportes <- TRUE
      message("🎓 Modo educativo: Enfoque pedagógico y compatibilidad LMS")
    }
  )
  
  return(config)
}

#' Validar configuración del paquete
#' 
#' @param config Configuración a validar
#' @keywords internal
validar_configuracion <- function(config) {
  
  errores <- character()
  advertencias <- character()
  
  # Validar tipos de ejercicios
  tipos_validos <- c("movimiento_lineal", "estadistica_descriptiva", "probabilidad_basica",
                    "variacion_exponencial", "geometria_analitica", "funciones_matematicas",
                    "estadistica_avanzada", "algebra_avanzada", "interpretacion_graficas")
  
  tipos_invalidos <- setdiff(config$generacion$tipos_ejercicios_disponibles, tipos_validos)
  if (length(tipos_invalidos) > 0) {
    errores <- c(errores, paste("Tipos de ejercicios no válidos:", paste(tipos_invalidos, collapse = ", ")))
  }
  
  # Validar configuración psicométrica
  if (config$psicometria$criterio_dificultad_min >= config$psicometria$criterio_dificultad_max) {
    errores <- c(errores, "Criterio de dificultad mínima debe ser menor que máxima")
  }
  
  # Validar configuración de rendimiento
  if (config$rendimiento$num_cores > parallel::detectCores()) {
    advertencias <- c(advertencias, "Número de cores especificado excede los disponibles")
    config$rendimiento$num_cores <- parallel::detectCores() - 1
  }
  
  # Validar directorios
  if (!dir.exists(dirname(config$rexams$directorio_templates))) {
    advertencias <- c(advertencias, "Directorio padre de templates no existe")
  }
  
  # Reportar errores y advertencias
  if (length(errores) > 0) {
    stop("Errores de configuración:\n", paste(errores, collapse = "\n"))
  }
  
  if (length(advertencias) > 0) {
    warning("Advertencias de configuración:\n", paste(advertencias, collapse = "\n"))
  }
}

#' Establecer opciones globales de R
#' 
#' @param config Configuración del paquete
#' @keywords internal
establecer_opciones_globales <- function(config) {
  
  # Opciones específicas del paquete
  options(
    ICFESMathExams.version = config$version_paquete,
    ICFESMathExams.modo = config$modo_operacion,
    ICFESMathExams.cache = config$generacion$cache_ejercicios,
    ICFESMathExams.validacion_estricta = config$validacion$validacion_estricta,
    ICFESMathExams.paralelizacion = config$rendimiento$usar_paralelizacion,
    ICFESMathExams.timeout = config$rendimiento$timeout_generacion
  )
  
  # Configurar zona horaria
  Sys.setenv(TZ = config$zona_horaria)
  
  # Configurar locale para español colombiano si está disponible
  tryCatch({
    if (config$idioma == "es") {
      Sys.setlocale("LC_TIME", "es_CO.UTF-8")
    }
  }, error = function(e) {
    # Ignorar si el locale no está disponible
  })
}

#' Inicializar componentes del paquete
#' 
#' @param config Configuración del paquete
#' @keywords internal
inicializar_componentes <- function(config) {
  
  # Crear directorios necesarios
  directorios <- c(
    config$rexams$directorio_templates,
    config$rexams$directorio_salida,
    "cache/",
    "logs/"
  )
  
  for (dir in directorios) {
    if (!dir.exists(dir)) {
      dir.create(dir, recursive = TRUE, showWarnings = FALSE)
    }
  }
  
  # Inicializar caché si está habilitado
  if (config$generacion$cache_ejercicios) {
    inicializar_cache()
  }
  
  # Configurar paralelización si está habilitada
  if (config$rendimiento$usar_paralelizacion) {
    configurar_paralelizacion(config$rendimiento$num_cores)
  }
  
  # Verificar dependencias opcionales
  verificar_dependencias_opcionales()
}

#' Obtener configuración actual
#' 
#' Obtiene la configuración actual del paquete
#' 
#' @param componente Componente específico a obtener (opcional)
#' @return Configuración completa o del componente especificado
#' @export
#' @examples
#' config <- obtener_configuracion()
#' config_rexams <- obtener_configuracion("rexams")
obtener_configuracion <- function(componente = NULL) {
  
  if (!exists("configuracion_icfes", envir = .ICFESMathExamsEnv)) {
    warning("Paquete no inicializado. Ejecute inicializar_icfes_optimizado() primero.")
    return(NULL)
  }
  
  config <- get("configuracion_icfes", envir = .ICFESMathExamsEnv)
  
  if (is.null(componente)) {
    return(config)
  } else if (componente %in% names(config)) {
    return(config[[componente]])
  } else {
    warning("Componente '", componente, "' no encontrado en la configuración")
    return(NULL)
  }
}

#' Actualizar configuración
#' 
#' Actualiza la configuración del paquete
#' 
#' @param nueva_configuracion Lista con nueva configuración
#' @param componente Componente específico a actualizar (opcional)
#' @return Configuración actualizada
#' @export
#' @examples
#' \dontrun{
#' actualizar_configuracion(list(validacion_estricta = FALSE), "validacion")
#' }
actualizar_configuracion <- function(nueva_configuracion, componente = NULL) {
  
  if (!exists("configuracion_icfes", envir = .ICFESMathExamsEnv)) {
    stop("Paquete no inicializado. Ejecute inicializar_icfes_optimizado() primero.")
  }
  
  config_actual <- get("configuracion_icfes", envir = .ICFESMathExamsEnv)
  
  if (is.null(componente)) {
    config_actualizada <- modifyList(config_actual, nueva_configuracion)
  } else if (componente %in% names(config_actual)) {
    config_actual[[componente]] <- modifyList(config_actual[[componente]], nueva_configuracion)
    config_actualizada <- config_actual
  } else {
    stop("Componente '", componente, "' no encontrado en la configuración")
  }
  
  # Validar nueva configuración
  validar_configuracion(config_actualizada)
  
  # Guardar configuración actualizada
  assign("configuracion_icfes", config_actualizada, envir = .ICFESMathExamsEnv)
  
  message("✅ Configuración actualizada correctamente")
  invisible(config_actualizada)
}

#' Entorno privado del paquete
#' @keywords internal
.ICFESMathExamsEnv <- new.env(parent = emptyenv())

#' Funciones auxiliares de inicialización
#' @keywords internal

inicializar_cache <- function() {
  if (!dir.exists("cache")) {
    dir.create("cache", showWarnings = FALSE)
  }
  message("💾 Caché inicializado")
}

configurar_paralelizacion <- function(num_cores) {
  if (requireNamespace("parallel", quietly = TRUE)) {
    options(mc.cores = num_cores)
    message("⚡ Paralelización configurada con ", num_cores, " cores")
  } else {
    warning("Paquete 'parallel' no disponible. Paralelización deshabilitada.")
  }
}

verificar_dependencias_opcionales <- function() {
  dependencias_opcionales <- list(
    "mirt" = "Análisis IRT avanzado",
    "ltm" = "Modelos de teoría de respuesta al ítem",
    "psychometric" = "Análisis psicométrico",
    "exams" = "Integración r-exams nativa",
    "tinytex" = "Compilación LaTeX",
    "reticulate" = "Integración Python"
  )
  
  for (pkg in names(dependencias_opcionales)) {
    if (requireNamespace(pkg, quietly = TRUE)) {
      message("✅ ", pkg, " disponible: ", dependencias_opcionales[[pkg]])
    } else {
      message("⚠️  ", pkg, " no disponible: ", dependencias_opcionales[[pkg]])
    }
  }
}
