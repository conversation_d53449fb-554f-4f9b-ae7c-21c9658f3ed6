#' @title Generadores de Ejercicios Matemáticos Avanzados ICFES
#' @description Funciones especializadas para generar ejercicios matemáticos
#' avanzados basados en el análisis del repositorio ICFES existente.

#' Generar ejercicio de geometría analítica
#' 
#' Crea ejercicios de construcciones geométricas, cálculo de áreas y perímetros
#' 
#' @param tipo_geometria Tipo específico ("area_triangulo", "perimetro_rectangulo", "construccion_geometrica")
#' @param parametros Lista de parámetros específicos
#' @return Lista con datos del ejercicio de geometría
#' @export
#' @examples
#' ejercicio <- generar_geometria_analitica("area_triangulo")
generar_geometria_analitica <- function(tipo_geometria = "area_triangulo", parametros = list()) {
  
  config <- generar_configuracion_defecto("geometria_analitica")
  config <- modifyList(config, parametros)
  
  resultado <- switch(tipo_geometria,
    "area_triangulo" = generar_area_triangulo(config),
    "perimetro_rectangulo" = generar_perimetro_rectangulo(config),
    "construccion_geometrica" = generar_construccion_geometrica(config),
    "volumen_prisma" = generar_volumen_prisma(config),
    stop("Tipo de geometría no válido: ", tipo_geometria)
  )
  
  # Metadatos específicos de geometría
  resultado$competencia <- "Pensamiento Espacial y Sistemas Geométricos"
  resultado$nivel_dificultad <- determinar_dificultad_geometria(resultado)
  resultado$contexto_icfes <- generar_contexto_colombiano("geometria")
  
  return(resultado)
}

#' Generar ejercicio de funciones matemáticas
#' 
#' Crea ejercicios de evaluación, composición y análisis de funciones
#' 
#' @param tipo_funcion Tipo específico ("funcion_compuesta", "evaluacion_funcion", "dominio_rango")
#' @param parametros Lista de parámetros específicos
#' @return Lista con datos del ejercicio de funciones
#' @export
#' @examples
#' ejercicio <- generar_funciones_matematicas("funcion_compuesta")
generar_funciones_matematicas <- function(tipo_funcion = "funcion_compuesta", parametros = list()) {
  
  config <- generar_configuracion_defecto("funciones")
  config <- modifyList(config, parametros)
  
  resultado <- switch(tipo_funcion,
    "funcion_compuesta" = generar_funcion_compuesta(config),
    "evaluacion_funcion" = generar_evaluacion_funcion(config),
    "dominio_rango" = generar_dominio_rango(config),
    "funcion_lineal" = generar_funcion_lineal(config),
    "funcion_cuadratica" = generar_funcion_cuadratica(config),
    stop("Tipo de función no válido: ", tipo_funcion)
  )
  
  # Metadatos específicos de funciones
  resultado$competencia <- "Pensamiento Variacional y Sistemas Algebraicos"
  resultado$nivel_dificultad <- determinar_dificultad_funciones(resultado)
  resultado$contexto_icfes <- generar_contexto_colombiano("funciones")
  
  return(resultado)
}

#' Generar ejercicio de estadística avanzada
#' 
#' Crea ejercicios de análisis estadístico, probabilidad y representaciones gráficas
#' 
#' @param tipo_estadistica Tipo específico ("distribucion_frecuencias", "probabilidad_condicional", "graficas_estadisticas")
#' @param parametros Lista de parámetros específicos
#' @return Lista con datos del ejercicio de estadística
#' @export
#' @examples
#' ejercicio <- generar_estadistica_avanzada("distribucion_frecuencias")
generar_estadistica_avanzada <- function(tipo_estadistica = "distribucion_frecuencias", parametros = list()) {
  
  config <- generar_configuracion_defecto("estadistica_avanzada")
  config <- modifyList(config, parametros)
  
  resultado <- switch(tipo_estadistica,
    "distribucion_frecuencias" = generar_distribucion_frecuencias(config),
    "probabilidad_condicional" = generar_probabilidad_condicional(config),
    "graficas_estadisticas" = generar_graficas_estadisticas(config),
    "medidas_dispersion" = generar_medidas_dispersion(config),
    "correlacion_regresion" = generar_correlacion_regresion(config),
    stop("Tipo de estadística no válido: ", tipo_estadistica)
  )
  
  # Metadatos específicos de estadística
  resultado$competencia <- "Pensamiento Aleatorio y Sistemas de Datos"
  resultado$nivel_dificultad <- determinar_dificultad_estadistica(resultado)
  resultado$contexto_icfes <- generar_contexto_colombiano("estadistica")
  
  return(resultado)
}

#' Generar ejercicio de álgebra avanzada
#' 
#' Crea ejercicios de polinomios, ecuaciones y sistemas algebraicos
#' 
#' @param tipo_algebra Tipo específico ("multiplicacion_polinomios", "factorizacion", "sistemas_ecuaciones")
#' @param parametros Lista de parámetros específicos
#' @return Lista con datos del ejercicio de álgebra
#' @export
#' @examples
#' ejercicio <- generar_algebra_avanzada("multiplicacion_polinomios")
generar_algebra_avanzada <- function(tipo_algebra = "multiplicacion_polinomios", parametros = list()) {
  
  config <- generar_configuracion_defecto("algebra_avanzada")
  config <- modifyList(config, parametros)
  
  resultado <- switch(tipo_algebra,
    "multiplicacion_polinomios" = generar_multiplicacion_polinomios(config),
    "factorizacion" = generar_factorizacion(config),
    "sistemas_ecuaciones" = generar_sistemas_ecuaciones(config),
    "ecuaciones_exponenciales" = generar_ecuaciones_exponenciales(config),
    "inecuaciones" = generar_inecuaciones(config),
    stop("Tipo de álgebra no válido: ", tipo_algebra)
  )
  
  # Metadatos específicos de álgebra
  resultado$competencia <- "Pensamiento Variacional y Sistemas Algebraicos"
  resultado$nivel_dificultad <- determinar_dificultad_algebra(resultado)
  resultado$contexto_icfes <- generar_contexto_colombiano("algebra")
  
  return(resultado)
}

#' Generar área de triángulo con coordenadas
#' 
#' @param config Configuración del ejercicio
#' @return Lista con datos del ejercicio
#' @keywords internal
generar_area_triangulo <- function(config) {
  
  # Generar coordenadas aleatorias
  x1 <- sample(-5:5, 1)
  y1 <- sample(-5:5, 1)
  x2 <- sample(-5:5, 1)
  y2 <- sample(-5:5, 1)
  x3 <- sample(-5:5, 1)
  y3 <- sample(-5:5, 1)
  
  # Calcular área usando fórmula de coordenadas
  area <- abs((x1*(y2-y3) + x2*(y3-y1) + x3*(y1-y2))/2)
  
  # Generar distractores
  area_incorrecta1 <- area + sample(1:3, 1)
  area_incorrecta2 <- area * 2
  area_incorrecta3 <- abs((x1*y2 + x2*y3 + x3*y1)/2)  # Error común
  
  opciones <- c(area, area_incorrecta1, area_incorrecta2, area_incorrecta3)
  opciones <- sample(opciones)
  
  list(
    tipo = "area_triangulo",
    enunciado = sprintf("Calcule el área del triángulo con vértices en A(%d,%d), B(%d,%d) y C(%d,%d).", 
                       x1, y1, x2, y2, x3, y3),
    coordenadas = list(A = c(x1, y1), B = c(x2, y2), C = c(x3, y3)),
    respuesta_correcta = area,
    opciones = opciones,
    solucion = sprintf("Usando la fórmula del área con coordenadas: |x₁(y₂-y₃) + x₂(y₃-y₁) + x₃(y₁-y₂)|/2 = %.1f", area),
    procedimiento = generar_procedimiento_area_triangulo(x1, y1, x2, y2, x3, y3, area)
  )
}

#' Generar función compuesta
#' 
#' @param config Configuración del ejercicio
#' @return Lista con datos del ejercicio
#' @keywords internal
generar_funcion_compuesta <- function(config) {
  
  # Generar funciones aleatorias
  a <- sample(2:5, 1)
  b <- sample(1:4, 1)
  c <- sample(-3:3, 1)
  d <- sample(2:4, 1)
  
  # f(x) = ax + b, g(x) = dx + c
  x_eval <- sample(-2:2, 1)
  
  # Calcular f(g(x)) y g(f(x))
  f_de_g <- a * (d * x_eval + c) + b
  g_de_f <- d * (a * x_eval + b) + c
  
  # Generar distractores
  distractor1 <- a * x_eval + d * x_eval + b + c  # Error común: suma directa
  distractor2 <- (a + d) * x_eval + (b + c)       # Error común: suma de funciones
  distractor3 <- a * d * x_eval + b * c           # Error común: producto
  
  opciones <- c(f_de_g, distractor1, distractor2, distractor3)
  opciones <- sample(opciones)
  
  list(
    tipo = "funcion_compuesta",
    enunciado = sprintf("Si f(x) = %dx + %d y g(x) = %dx + %d, calcule (f∘g)(%d).", 
                       a, b, d, c, x_eval),
    funciones = list(f = c(a, b), g = c(d, c)),
    x_evaluacion = x_eval,
    respuesta_correcta = f_de_g,
    opciones = opciones,
    solucion = sprintf("(f∘g)(x) = f(g(x)) = f(%dx + %d) = %d(%dx + %d) + %d = %d", 
                      d, c, a, d, c, b, f_de_g),
    procedimiento = generar_procedimiento_funcion_compuesta(a, b, d, c, x_eval, f_de_g)
  )
}

#' Generar distribución de frecuencias
#' 
#' @param config Configuración del ejercicio
#' @return Lista con datos del ejercicio
#' @keywords internal
generar_distribucion_frecuencias <- function(config) {
  
  # Generar datos categóricos
  categorias <- c("Excelente", "Bueno", "Regular", "Deficiente")
  n_total <- sample(80:120, 1)
  
  # Generar frecuencias que sumen n_total
  frecuencias <- sample(10:30, 4)
  frecuencias <- round(frecuencias * n_total / sum(frecuencias))
  frecuencias[4] <- n_total - sum(frecuencias[1:3])  # Ajustar última frecuencia
  
  # Calcular frecuencias relativas
  freq_relativas <- round(frecuencias / n_total, 3)
  
  # Pregunta sobre frecuencia relativa de una categoría específica
  categoria_pregunta <- sample(categorias, 1)
  indice_categoria <- which(categorias == categoria_pregunta)
  respuesta_correcta <- freq_relativas[indice_categoria]
  
  # Generar distractores
  distractor1 <- frecuencias[indice_categoria] / 100  # Error común: dividir por 100
  distractor2 <- frecuencias[indice_categoria]        # Error común: frecuencia absoluta
  distractor3 <- round(respuesta_correcta + 0.1, 3)  # Error de cálculo
  
  opciones <- c(respuesta_correcta, distractor1, distractor2, distractor3)
  opciones <- sample(opciones)
  
  list(
    tipo = "distribucion_frecuencias",
    enunciado = sprintf("En una encuesta sobre calidad del servicio a %d personas, se obtuvieron los siguientes resultados: %s: %d, %s: %d, %s: %d, %s: %d. ¿Cuál es la frecuencia relativa de la categoría '%s'?",
                       n_total, categorias[1], frecuencias[1], categorias[2], frecuencias[2], 
                       categorias[3], frecuencias[3], categorias[4], frecuencias[4], categoria_pregunta),
    datos = data.frame(categoria = categorias, frecuencia = frecuencias, freq_relativa = freq_relativas),
    categoria_pregunta = categoria_pregunta,
    respuesta_correcta = respuesta_correcta,
    opciones = opciones,
    solucion = sprintf("Frecuencia relativa = Frecuencia absoluta / Total = %d / %d = %.3f", 
                      frecuencias[indice_categoria], n_total, respuesta_correcta),
    procedimiento = generar_procedimiento_frecuencias(categorias, frecuencias, n_total, categoria_pregunta)
  )
}

#' Generar multiplicación de polinomios
#' 
#' @param config Configuración del ejercicio
#' @return Lista con datos del ejercicio
#' @keywords internal
generar_multiplicacion_polinomios <- function(config) {
  
  # Generar coeficientes aleatorios
  a1 <- sample(2:5, 1)
  b1 <- sample(1:4, 1)
  a2 <- sample(2:4, 1)
  b2 <- sample(-3:3, 1)
  
  # (a1*x + b1)(a2*x + b2) = a1*a2*x² + (a1*b2 + b1*a2)*x + b1*b2
  coef_x2 <- a1 * a2
  coef_x1 <- a1 * b2 + b1 * a2
  coef_x0 <- b1 * b2
  
  # Generar distractores
  distractor1_x2 <- a1 + a2  # Error común: suma en lugar de producto
  distractor1_x1 <- b1 + b2
  distractor1_x0 <- a1 * b1 + a2 * b2
  
  distractor2_x2 <- coef_x2
  distractor2_x1 <- a1 * b1 + a2 * b2  # Error en término medio
  distractor2_x0 <- coef_x0
  
  # Formatear polinomios
  polinomio_correcto <- formatear_polinomio(coef_x2, coef_x1, coef_x0)
  distractor1 <- formatear_polinomio(distractor1_x2, distractor1_x1, distractor1_x0)
  distractor2 <- formatear_polinomio(distractor2_x2, distractor2_x1, distractor2_x0)
  distractor3 <- formatear_polinomio(coef_x2, coef_x1 + 1, coef_x0)  # Error menor
  
  opciones <- c(polinomio_correcto, distractor1, distractor2, distractor3)
  opciones <- sample(opciones)
  
  list(
    tipo = "multiplicacion_polinomios",
    enunciado = sprintf("Efectúe la multiplicación (%dx + %d)(%dx + %d).", a1, b1, a2, b2),
    polinomios = list(p1 = c(a1, b1), p2 = c(a2, b2)),
    respuesta_correcta = polinomio_correcto,
    opciones = opciones,
    solucion = sprintf("(%dx + %d)(%dx + %d) = %dx² + %dx + %d = %s", 
                      a1, b1, a2, b2, coef_x2, coef_x1, coef_x0, polinomio_correcto),
    procedimiento = generar_procedimiento_multiplicacion_polinomios(a1, b1, a2, b2, coef_x2, coef_x1, coef_x0)
  )
}

#' Formatear polinomio para mostrar
#' 
#' @param a Coeficiente de x²
#' @param b Coeficiente de x
#' @param c Término independiente
#' @return String con el polinomio formateado
#' @keywords internal
formatear_polinomio <- function(a, b, c) {
  resultado <- ""
  
  # Término x²
  if (a != 0) {
    if (a == 1) {
      resultado <- "x²"
    } else if (a == -1) {
      resultado <- "-x²"
    } else {
      resultado <- paste0(a, "x²")
    }
  }
  
  # Término x
  if (b != 0) {
    if (resultado != "") {
      if (b > 0) {
        resultado <- paste0(resultado, " + ")
      } else {
        resultado <- paste0(resultado, " - ")
        b <- abs(b)
      }
    } else if (b < 0) {
      resultado <- "-"
      b <- abs(b)
    }
    
    if (b == 1) {
      resultado <- paste0(resultado, "x")
    } else {
      resultado <- paste0(resultado, b, "x")
    }
  }
  
  # Término independiente
  if (c != 0) {
    if (resultado != "") {
      if (c > 0) {
        resultado <- paste0(resultado, " + ", c)
      } else {
        resultado <- paste0(resultado, " - ", abs(c))
      }
    } else {
      resultado <- as.character(c)
    }
  }
  
  if (resultado == "") resultado <- "0"
  return(resultado)
}

#' Determinar dificultad de ejercicio de geometría
#'
#' @param ejercicio Lista con datos del ejercicio
#' @return Nivel de dificultad (1-5)
#' @keywords internal
determinar_dificultad_geometria <- function(ejercicio) {
  if (ejercicio$tipo == "area_triangulo") {
    # Dificultad basada en complejidad de coordenadas
    max_coord <- max(abs(unlist(ejercicio$coordenadas)))
    if (max_coord <= 3) return(2)
    if (max_coord <= 5) return(3)
    return(4)
  }
  return(3)  # Dificultad por defecto
}

#' Determinar dificultad de ejercicio de funciones
#'
#' @param ejercicio Lista con datos del ejercicio
#' @return Nivel de dificultad (1-5)
#' @keywords internal
determinar_dificultad_funciones <- function(ejercicio) {
  if (ejercicio$tipo == "funcion_compuesta") {
    # Dificultad basada en complejidad de coeficientes
    coefs <- unlist(ejercicio$funciones)
    if (all(abs(coefs) <= 3)) return(3)
    if (any(abs(coefs) > 5)) return(4)
    return(3)
  }
  return(3)  # Dificultad por defecto
}

#' Determinar dificultad de ejercicio de estadística
#'
#' @param ejercicio Lista con datos del ejercicio
#' @return Nivel de dificultad (1-5)
#' @keywords internal
determinar_dificultad_estadistica <- function(ejercicio) {
  if (ejercicio$tipo == "distribucion_frecuencias") {
    # Dificultad basada en tamaño de muestra
    n_total <- sum(ejercicio$datos$frecuencia)
    if (n_total <= 50) return(2)
    if (n_total <= 100) return(3)
    return(4)
  }
  return(3)  # Dificultad por defecto
}

#' Determinar dificultad de ejercicio de álgebra
#'
#' @param ejercicio Lista con datos del ejercicio
#' @return Nivel de dificultad (1-5)
#' @keywords internal
determinar_dificultad_algebra <- function(ejercicio) {
  if (ejercicio$tipo == "multiplicacion_polinomios") {
    # Dificultad basada en complejidad de coeficientes
    coefs <- unlist(ejercicio$polinomios)
    if (all(abs(coefs) <= 3)) return(2)
    if (any(abs(coefs) > 5)) return(4)
    return(3)
  }
  return(3)  # Dificultad por defecto
}

#' Generar contexto colombiano para ejercicios
#'
#' @param area_matematica Área matemática del ejercicio
#' @return String con contexto colombiano apropiado
#' @keywords internal
generar_contexto_colombiano <- function(area_matematica) {
  contextos <- list(
    geometria = c(
      "Un arquitecto en Bogotá diseña un parque triangular",
      "En la construcción de un edificio en Medellín",
      "Para el diseño de una plaza en Cartagena",
      "En la planificación urbana de Cali"
    ),
    funciones = c(
      "El costo de transporte público en Colombia",
      "La relación entre temperatura y altitud en los Andes",
      "El crecimiento poblacional en ciudades colombianas",
      "La producción de café en función del tiempo"
    ),
    estadistica = c(
      "Encuesta sobre preferencias electorales en Colombia",
      "Estudio de accidentalidad vial en carreteras nacionales",
      "Análisis de rendimiento académico en colegios ICFES",
      "Investigación sobre hábitos alimentarios en estudiantes"
    ),
    algebra = c(
      "Cálculo de costos de producción en una empresa colombiana",
      "Optimización de recursos en agricultura cafetera",
      "Análisis financiero de inversiones en Colombia",
      "Modelado matemático de fenómenos económicos nacionales"
    )
  )

  if (area_matematica %in% names(contextos)) {
    return(sample(contextos[[area_matematica]], 1))
  }
  return("Contexto matemático aplicado a situaciones colombianas")
}

#' Generar procedimiento detallado para área de triángulo
#'
#' @param x1,y1,x2,y2,x3,y3 Coordenadas de los vértices
#' @param area Área calculada
#' @return Lista con pasos del procedimiento
#' @keywords internal
generar_procedimiento_area_triangulo <- function(x1, y1, x2, y2, x3, y3, area) {
  list(
    paso1 = "Identificar las coordenadas de los tres vértices del triángulo",
    paso2 = sprintf("A(%d,%d), B(%d,%d), C(%d,%d)", x1, y1, x2, y2, x3, y3),
    paso3 = "Aplicar la fórmula: Área = |x₁(y₂-y₃) + x₂(y₃-y₁) + x₃(y₁-y₂)|/2",
    paso4 = sprintf("Sustituir: |%d(%d-%d) + %d(%d-%d) + %d(%d-%d)|/2",
                   x1, y2, y3, x2, y3, y1, x3, y1, y2),
    paso5 = sprintf("Calcular: |%d + %d + %d|/2 = %.1f",
                   x1*(y2-y3), x2*(y3-y1), x3*(y1-y2), area),
    resultado = sprintf("El área del triángulo es %.1f unidades cuadradas", area)
  )
}

#' Generar procedimiento para función compuesta
#'
#' @param a,b,d,c Coeficientes de las funciones
#' @param x_eval Valor de evaluación
#' @param resultado Resultado final
#' @return Lista con pasos del procedimiento
#' @keywords internal
generar_procedimiento_funcion_compuesta <- function(a, b, d, c, x_eval, resultado) {
  list(
    paso1 = sprintf("Dadas f(x) = %dx + %d y g(x) = %dx + %d", a, b, d, c),
    paso2 = sprintf("Calcular (f∘g)(%d) = f(g(%d))", x_eval, x_eval),
    paso3 = sprintf("Primero evaluar g(%d) = %d(%d) + %d = %d",
                   x_eval, d, x_eval, c, d*x_eval + c),
    paso4 = sprintf("Luego evaluar f(%d) = %d(%d) + %d = %d",
                   d*x_eval + c, a, d*x_eval + c, b, resultado),
    resultado = sprintf("Por lo tanto, (f∘g)(%d) = %d", x_eval, resultado)
  )
}

#' Generar procedimiento para distribución de frecuencias
#'
#' @param categorias Vector de categorías
#' @param frecuencias Vector de frecuencias absolutas
#' @param n_total Total de observaciones
#' @param categoria_pregunta Categoría sobre la que se pregunta
#' @return Lista con pasos del procedimiento
#' @keywords internal
generar_procedimiento_frecuencias <- function(categorias, frecuencias, n_total, categoria_pregunta) {
  indice <- which(categorias == categoria_pregunta)
  freq_abs <- frecuencias[indice]
  freq_rel <- freq_abs / n_total

  list(
    paso1 = "Identificar la frecuencia absoluta de la categoría solicitada",
    paso2 = sprintf("Frecuencia absoluta de '%s' = %d", categoria_pregunta, freq_abs),
    paso3 = sprintf("Total de observaciones = %d", n_total),
    paso4 = "Aplicar fórmula: Frecuencia relativa = Frecuencia absoluta / Total",
    paso5 = sprintf("Frecuencia relativa = %d / %d = %.3f", freq_abs, n_total, freq_rel),
    resultado = sprintf("La frecuencia relativa de '%s' es %.3f", categoria_pregunta, freq_rel)
  )
}

#' Generar procedimiento para multiplicación de polinomios
#'
#' @param a1,b1,a2,b2 Coeficientes de los polinomios
#' @param coef_x2,coef_x1,coef_x0 Coeficientes del resultado
#' @return Lista con pasos del procedimiento
#' @keywords internal
generar_procedimiento_multiplicacion_polinomios <- function(a1, b1, a2, b2, coef_x2, coef_x1, coef_x0) {
  list(
    paso1 = sprintf("Multiplicar (%dx + %d)(%dx + %d)", a1, b1, a2, b2),
    paso2 = "Aplicar propiedad distributiva: (a + b)(c + d) = ac + ad + bc + bd",
    paso3 = sprintf("%dx·%dx + %dx·%d + %d·%dx + %d·%d", a1, a2, a1, b2, b1, a2, b1, b2),
    paso4 = sprintf("%dx² + %dx + %dx + %d", coef_x2, a1*b2, b1*a2, coef_x0),
    paso5 = sprintf("Simplificar términos semejantes: %dx² + %dx + %d", coef_x2, coef_x1, coef_x0),
    resultado = sprintf("El resultado es %s", formatear_polinomio(coef_x2, coef_x1, coef_x0))
  )
}

#' Generar ejercicio de interpretación de gráficas
#'
#' Crea ejercicios de interpretación de gráficas de líneas basados en poblaciones de países
#'
#' @param tipo_grafica Tipo específico ("poblaciones_paises", "tendencias_economicas", "datos_climaticos")
#' @param parametros Lista de parámetros específicos
#' @return Lista con datos del ejercicio de gráficas
#' @export
#' @examples
#' ejercicio <- generar_interpretacion_graficas("poblaciones_paises")
generar_interpretacion_graficas <- function(tipo_grafica = "poblaciones_paises", parametros = list()) {

  config <- generar_configuracion_defecto("interpretacion_graficas")
  config <- modifyList(config, parametros)

  resultado <- switch(tipo_grafica,
    "poblaciones_paises" = generar_poblaciones_paises(config),
    "tendencias_economicas" = generar_tendencias_economicas(config),
    "datos_climaticos" = generar_datos_climaticos(config),
    stop("Tipo de gráfica no válido: ", tipo_grafica)
  )

  # Metadatos específicos de interpretación de gráficas
  resultado$competencia <- "Interpretación y Representación"
  resultado$nivel_dificultad <- determinar_dificultad_graficas(resultado)
  resultado$contexto_icfes <- generar_contexto_colombiano("graficas")
  resultado$eje_axial <- "eje3"
  resultado$componente <- "aleatorio"

  return(resultado)
}

#' Generar ejercicio de poblaciones de países
#'
#' @param config Configuración del ejercicio
#' @return Lista con datos del ejercicio
#' @keywords internal
generar_poblaciones_paises <- function(config) {

  # Aleatorizar nombres de países (enfoque colombiano y latinoamericano)
  nombres_paises <- list(
    latinoamerica = c("Colombia", "Brasil", "Argentina", "Chile", "Perú", "Venezuela", "Ecuador", "Uruguay"),
    europa = c("España", "Francia", "Italia", "Alemania", "Portugal", "Holanda", "Suecia", "Noruega"),
    asia = c("Japón", "Corea del Sur", "Tailandia", "Vietnam", "Filipinas", "Indonesia", "Malasia"),
    africa = c("Nigeria", "Egipto", "Sudáfrica", "Kenia", "Ghana", "Marruecos", "Túnez"),
    otros = c("Canadá", "Australia", "Nueva Zelanda", "Estados Unidos")
  )

  # Seleccionar países con énfasis en Latinoamérica
  paises_latinoamerica <- sample(nombres_paises$latinoamerica, 2)
  paises_otros <- sample(unlist(nombres_paises[2:5]), 3)
  paises_seleccionados <- c(paises_latinoamerica, paises_otros)

  # Aleatorizar año de intersección (respuesta correcta)
  años_interseccion <- c(1986, 1998, 2004, 1992, 1995, 2001, 1989, 1996, 2002, 1988, 2006, 2008)
  año_interseccion <- sample(años_interseccion, 1)

  # Aleatorizar qué dos países se intersectan
  paises_interseccion <- sample(1:5, 2)
  pais_a <- min(paises_interseccion)
  pais_b <- max(paises_interseccion)

  # Poblaciones base realistas (en millones)
  pob_inicial_a <- sample(seq(20, 25, 0.5), 1)
  pob_inicial_b <- sample(seq(28, 32, 0.5), 1)

  # Tendencias de crecimiento diferenciadas
  tasa_a <- sample(seq(0.8, 1.2, 0.1), 1)  # Crecimiento más rápido
  tasa_b <- sample(seq(0.3, 0.6, 0.1), 1)  # Crecimiento más lento

  # Generar distractores inteligentes
  respuesta_correcta <- año_interseccion

  distractores <- c(
    1960,  # Inicio del período
    2013,  # Final del período
    respuesta_correcta + sample(c(-8, -6, 6, 8), 1),  # Error de lectura visual
    1987,  # Punto medio del período
    1990,  # Década de referencia
    respuesta_correcta + sample(c(-12, -10, 10, 12), 1)  # Error de interpretación
  )

  # Crear opciones únicas
  todas_opciones <- unique(c(respuesta_correcta, distractores))
  todas_opciones <- todas_opciones[todas_opciones >= 1960 & todas_opciones <= 2013]

  # Seleccionar 4 opciones finales
  if (length(todas_opciones) >= 4) {
    opciones_finales <- sample(todas_opciones, 4)
  } else {
    while (length(todas_opciones) < 4) {
      nuevo_distractor <- respuesta_correcta + sample(-15:15, 1)
      if (!nuevo_distractor %in% todas_opciones && nuevo_distractor >= 1960 && nuevo_distractor <= 2013) {
        todas_opciones <- c(todas_opciones, nuevo_distractor)
      }
    }
    opciones_finales <- sample(todas_opciones, 4)
  }

  # Asegurar que la respuesta correcta esté incluida
  if (!respuesta_correcta %in% opciones_finales) {
    opciones_finales[1] <- respuesta_correcta
  }

  opciones_finales <- sort(opciones_finales)

  # Crear enunciado contextualizado
  enunciado <- sprintf(
    "La siguiente gráfica muestra la evolución de la población (en millones de habitantes) de cinco países entre 1960 y 2013. Según la información presentada, ¿en qué año aproximadamente las poblaciones de %s y %s fueron iguales?",
    paises_seleccionados[pais_a],
    paises_seleccionados[pais_b]
  )

  list(
    tipo = "poblaciones_paises",
    enunciado = enunciado,
    paises = paises_seleccionados,
    año_interseccion = año_interseccion,
    pais_a = pais_a,
    pais_b = pais_b,
    pob_inicial_a = pob_inicial_a,
    pob_inicial_b = pob_inicial_b,
    tasa_a = tasa_a,
    tasa_b = tasa_b,
    respuesta_correcta = respuesta_correcta,
    opciones = opciones_finales,
    solucion = sprintf("Analizando la gráfica, las líneas de %s y %s se intersectan aproximadamente en el año %d.",
                      paises_seleccionados[pais_a], paises_seleccionados[pais_b], respuesta_correcta),
    procedimiento = generar_procedimiento_graficas(paises_seleccionados, pais_a, pais_b, respuesta_correcta),
    metadatos_grafica = list(
      periodo = c(1960, 2013),
      unidad = "millones de habitantes",
      tipo_grafica = "líneas",
      ejes = list(x = "Año", y = "Población")
    )
  )
}

#' Generar procedimiento para interpretación de gráficas
#'
#' @param paises Vector de nombres de países
#' @param pais_a Índice del primer país
#' @param pais_b Índice del segundo país
#' @param año_respuesta Año de intersección
#' @return Lista con pasos del procedimiento
#' @keywords internal
generar_procedimiento_graficas <- function(paises, pais_a, pais_b, año_respuesta) {
  list(
    paso1 = "Identificar las líneas correspondientes a cada país en la gráfica",
    paso2 = sprintf("Localizar las líneas de %s y %s", paises[pais_a], paises[pais_b]),
    paso3 = "Buscar el punto donde las dos líneas se cruzan (intersección)",
    paso4 = "Proyectar verticalmente desde el punto de intersección hacia el eje X (años)",
    paso5 = sprintf("Leer el valor en el eje X: aproximadamente %d", año_respuesta),
    resultado = sprintf("Las poblaciones de %s y %s fueron iguales en %d",
                       paises[pais_a], paises[pais_b], año_respuesta),
    competencia_evaluada = "Interpretación y representación de información estadística en gráficas de líneas"
  )
}

#' Determinar dificultad de ejercicio de gráficas
#'
#' @param ejercicio Lista con datos del ejercicio
#' @return Nivel de dificultad (1-5)
#' @keywords internal
determinar_dificultad_graficas <- function(ejercicio) {
  # Dificultad basada en la diferencia entre las tasas de crecimiento
  diferencia_tasas <- abs(ejercicio$tasa_a - ejercicio$tasa_b)

  if (diferencia_tasas >= 0.5) return(2)  # Fácil: líneas muy diferentes
  if (diferencia_tasas >= 0.3) return(3)  # Medio: diferencia moderada
  if (diferencia_tasas >= 0.1) return(4)  # Difícil: líneas similares
  return(5)  # Muy difícil: líneas casi paralelas
}

# Funciones auxiliares para otros tipos de gráficas (para expansión futura)
generar_tendencias_economicas <- function(config) {
  # Placeholder para futuras implementaciones
  stop("Tipo de ejercicio en desarrollo: tendencias_economicas")
}

generar_datos_climaticos <- function(config) {
  # Placeholder para futuras implementaciones
  stop("Tipo de ejercicio en desarrollo: datos_climaticos")
}

#' Determinar dificultad de ejercicios de gráficas
#'
#' @param resultado Lista con datos del ejercicio
#' @return Nivel de dificultad
#' @keywords internal
determinar_dificultad_graficas <- function(resultado) {
  # Determinar dificultad basada en características del ejercicio
  num_paises <- length(resultado$paises)
  rango_años <- abs(resultado$año_interseccion - 1960)

  if (num_paises <= 3 && rango_años <= 20) {
    "facil"
  } else if (num_paises <= 5 && rango_años <= 35) {
    "intermedio"
  } else {
    "dificil"
  }
}

#' Generar contexto colombiano para ejercicios
#'
#' @param tipo Tipo de ejercicio
#' @return Contexto colombiano apropiado
#' @keywords internal
generar_contexto_colombiano <- function(tipo) {
  contextos <- list(
    graficas = c(
      "Análisis demográfico para políticas públicas colombianas",
      "Estudio comparativo de desarrollo regional en América Latina",
      "Investigación sobre migración y crecimiento poblacional",
      "Análisis estadístico para el DANE (Departamento Nacional de Estadística)"
    )
  )

  sample(contextos[[tipo]] %||% contextos$graficas, 1)
}

#' Generar procedimiento para ejercicios de gráficas
#'
#' @param paises Vector de países
#' @param pais_a Índice del primer país
#' @param pais_b Índice del segundo país
#' @param respuesta_correcta Año de intersección
#' @return Procedimiento detallado
#' @keywords internal
generar_procedimiento_graficas <- function(paises, pais_a, pais_b, respuesta_correcta) {
  sprintf(
    "Para resolver este ejercicio: 1) Identificar las líneas correspondientes a %s y %s en la gráfica. 2) Buscar el punto donde estas líneas se cruzan. 3) Leer el valor en el eje horizontal (años) en el punto de intersección. 4) El año aproximado es %d.",
    paises[pais_a], paises[pais_b], respuesta_correcta
  )
}
