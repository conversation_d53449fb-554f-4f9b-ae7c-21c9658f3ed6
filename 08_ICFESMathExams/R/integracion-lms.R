#' @title Integración con LMS y Exportación Múltiple
#' @description Funciones para integrar ICFESMathExams con sistemas de gestión
#' de aprendizaje (LMS) y exportar a múltiples formatos estándar.

#' Exportar examen a formato LMS
#' 
#' Exporta ejercicios ICFESMathExams a formatos compatibles con LMS populares
#' 
#' @param ejercicios Lista de ejercicios a exportar
#' @param formato_lms Formato LMS ("moodle", "blackboard", "canvas", "brightspace", "scorm")
#' @param configuracion_exportacion Lista con configuración específica
#' @param directorio_salida Directorio donde guardar archivos exportados
#' @return Lista con información de archivos exportados
#' @export
#' @examples
#' \dontrun{
#' ejercicios <- ejecutar_generacion_datos(c("algebra_avanzada"), 10)
#' archivos <- exportar_lms(ejercicios, "moodle", directorio_salida = "export/")
#' }
exportar_lms <- function(ejercicios, formato_lms = "moodle", 
                        configuracion_exportacion = list(),
                        directorio_salida = "lms_export/") {
  
  # Validar formato LMS
  formatos_soportados <- c("moodle", "blackboard", "canvas", "brightspace", "scorm", "qti")
  if (!formato_lms %in% formatos_soportados) {
    stop("Formato LMS no soportado. Formatos disponibles: ", 
         paste(formatos_soportados, collapse = ", "))
  }
  
  # Crear directorio de salida
  if (!dir.exists(directorio_salida)) {
    dir.create(directorio_salida, recursive = TRUE)
  }
  
  # Configuración por defecto
  config_default <- list(
    nombre_curso = "Matemáticas ICFES",
    categoria = "Evaluaciones",
    tiempo_limite = 90,  # minutos
    intentos_permitidos = 2,
    mostrar_retroalimentacion = TRUE,
    barajar_preguntas = TRUE,
    barajar_opciones = TRUE
  )
  
  config <- modifyList(config_default, configuracion_exportacion)
  
  # Exportar según formato
  resultado_exportacion <- switch(formato_lms,
    "moodle" = exportar_moodle_xml(ejercicios, config, directorio_salida),
    "blackboard" = exportar_blackboard_zip(ejercicios, config, directorio_salida),
    "canvas" = exportar_canvas_qti(ejercicios, config, directorio_salida),
    "brightspace" = exportar_brightspace_zip(ejercicios, config, directorio_salida),
    "scorm" = exportar_scorm_package(ejercicios, config, directorio_salida),
    "qti" = exportar_qti_xml(ejercicios, config, directorio_salida),
    stop("Formato no implementado: ", formato_lms)
  )
  
  # Agregar metadatos de exportación
  resultado_exportacion$metadatos <- list(
    formato_lms = formato_lms,
    num_ejercicios = length(ejercicios),
    fecha_exportacion = Sys.time(),
    configuracion_usada = config,
    directorio_salida = directorio_salida
  )
  
  class(resultado_exportacion) <- "exportacion_lms_icfes"
  return(resultado_exportacion)
}

#' Exportar a formato Moodle XML
#' 
#' @param ejercicios Lista de ejercicios
#' @param config Configuración de exportación
#' @param directorio Directorio de salida
#' @return Lista con información de exportación
#' @keywords internal
exportar_moodle_xml <- function(ejercicios, config, directorio) {
  
  # Crear estructura XML para Moodle
  xml_content <- c(
    '<?xml version="1.0" encoding="UTF-8"?>',
    '<quiz>',
    paste0('<!-- Generado por ICFESMathExams - ', Sys.time(), ' -->'),
    ''
  )
  
  # Agregar cada ejercicio como pregunta
  for (i in seq_along(ejercicios)) {
    ejercicio <- ejercicios[[i]]
    pregunta_xml <- generar_pregunta_moodle_xml(ejercicio, i, config)
    xml_content <- c(xml_content, pregunta_xml, '')
  }
  
  xml_content <- c(xml_content, '</quiz>')
  
  # Escribir archivo XML
  archivo_xml <- file.path(directorio, paste0(config$nombre_curso, "_moodle.xml"))
  writeLines(xml_content, archivo_xml, useBytes = TRUE)
  
  # Crear archivo de configuración adicional
  config_file <- crear_archivo_configuracion_moodle(config, directorio)
  
  return(list(
    archivo_principal = archivo_xml,
    archivo_configuracion = config_file,
    formato = "Moodle XML",
    instrucciones = "Importar en Moodle: Administración > Banco de preguntas > Importar"
  ))
}

#' Generar pregunta en formato Moodle XML
#' 
#' @param ejercicio Datos del ejercicio
#' @param numero Número de la pregunta
#' @param config Configuración
#' @return Vector de líneas XML
#' @keywords internal
generar_pregunta_moodle_xml <- function(ejercicio, numero, config) {
  
  # Determinar tipo de pregunta Moodle
  tipo_moodle <- determinar_tipo_moodle(ejercicio)
  
  xml_lines <- c(
    paste0('  <question type="', tipo_moodle, '">'),
    paste0('    <name><text>Ejercicio ICFES ', numero, '</text></name>'),
    paste0('    <questiontext format="html">'),
    paste0('      <text><![CDATA[', generar_texto_pregunta_html(ejercicio), ']]></text>'),
    '    </questiontext>',
    paste0('    <generalfeedback format="html">'),
    paste0('      <text><![CDATA[', generar_retroalimentacion_html(ejercicio), ']]></text>'),
    '    </generalfeedback>',
    '    <defaultgrade>1</defaultgrade>',
    '    <penalty>0.1</penalty>',
    '    <hidden>0</hidden>'
  )
  
  # Agregar opciones específicas según tipo
  if (tipo_moodle == "multichoice") {
    xml_lines <- c(xml_lines, generar_opciones_multichoice_xml(ejercicio, config))
  } else if (tipo_moodle == "numerical") {
    xml_lines <- c(xml_lines, generar_respuesta_numerica_xml(ejercicio))
  }
  
  xml_lines <- c(xml_lines, '  </question>')
  return(xml_lines)
}

#' Exportar a formato QTI (Question & Test Interoperability)
#' 
#' @param ejercicios Lista de ejercicios
#' @param config Configuración de exportación
#' @param directorio Directorio de salida
#' @return Lista con información de exportación
#' @keywords internal
exportar_qti_xml <- function(ejercicios, config, directorio) {
  
  # Crear estructura QTI 2.1
  qti_content <- c(
    '<?xml version="1.0" encoding="UTF-8"?>',
    '<assessmentTest xmlns="http://www.imsglobal.org/xsd/imsqti_v2p1"',
    '                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"',
    '                xsi:schemaLocation="http://www.imsglobal.org/xsd/imsqti_v2p1 http://www.imsglobal.org/xsd/qti/qtiv2p1/imsqti_v2p1.xsd"',
    paste0('                identifier="ICFES_', format(Sys.time(), "%Y%m%d_%H%M%S"), '"'),
    paste0('                title="', config$nombre_curso, '">'),
    '',
    '  <outcomeDeclaration identifier="SCORE" cardinality="single" baseType="float">',
    '    <defaultValue><value>0</value></defaultValue>',
    '  </outcomeDeclaration>',
    '',
    '  <testPart identifier="testPart1" navigationMode="linear" submissionMode="individual">',
    paste0('    <timeLimits maxTime="', config$tiempo_limite * 60, '"/>'),
    '    <assessmentSection identifier="section1" required="true" fixed="false">',
    paste0('      <title>', config$categoria, '</title>')
  )
  
  # Agregar cada ejercicio como ítem
  for (i in seq_along(ejercicios)) {
    ejercicio <- ejercicios[[i]]
    item_ref <- generar_item_reference_qti(ejercicio, i)
    qti_content <- c(qti_content, item_ref)
  }
  
  qti_content <- c(qti_content,
    '    </assessmentSection>',
    '  </testPart>',
    '</assessmentTest>'
  )
  
  # Escribir archivo QTI
  archivo_qti <- file.path(directorio, paste0(config$nombre_curso, "_qti.xml"))
  writeLines(qti_content, archivo_qti, useBytes = TRUE)
  
  # Crear archivos de ítems individuales
  archivos_items <- crear_archivos_items_qti(ejercicios, directorio)
  
  # Crear manifiesto IMS
  archivo_manifiesto <- crear_manifiesto_ims(config, directorio, archivo_qti, archivos_items)
  
  return(list(
    archivo_principal = archivo_qti,
    archivos_items = archivos_items,
    archivo_manifiesto = archivo_manifiesto,
    formato = "QTI 2.1",
    instrucciones = "Compatible con Canvas, Brightspace y otros LMS que soporten QTI"
  ))
}

#' Exportar a formato SCORM
#' 
#' @param ejercicios Lista de ejercicios
#' @param config Configuración de exportación
#' @param directorio Directorio de salida
#' @return Lista con información de exportación
#' @keywords internal
exportar_scorm_package <- function(ejercicios, config, directorio) {
  
  # Crear estructura de directorios SCORM
  scorm_dir <- file.path(directorio, "scorm_package")
  dir.create(scorm_dir, showWarnings = FALSE)
  
  # Crear manifiesto SCORM
  manifiesto_scorm <- crear_manifiesto_scorm(config, ejercicios)
  writeLines(manifiesto_scorm, file.path(scorm_dir, "imsmanifest.xml"))
  
  # Crear contenido HTML
  html_content <- generar_contenido_html_scorm(ejercicios, config)
  writeLines(html_content, file.path(scorm_dir, "index.html"))
  
  # Crear archivos JavaScript para SCORM API
  js_scorm <- generar_javascript_scorm()
  writeLines(js_scorm, file.path(scorm_dir, "scorm_api.js"))
  
  # Crear CSS
  css_content <- generar_css_scorm()
  writeLines(css_content, file.path(scorm_dir, "styles.css"))
  
  # Crear archivo ZIP
  archivo_zip <- file.path(directorio, paste0(config$nombre_curso, "_scorm.zip"))
  
  # Comprimir usando system zip si está disponible
  if (Sys.which("zip") != "") {
    system(sprintf('cd "%s" && zip -r "%s" scorm_package/', 
                   directorio, basename(archivo_zip)))
  } else {
    warning("Comando 'zip' no disponible. Paquete SCORM creado sin comprimir.")
    archivo_zip <- scorm_dir
  }
  
  return(list(
    archivo_principal = archivo_zip,
    directorio_contenido = scorm_dir,
    formato = "SCORM 1.2",
    instrucciones = "Subir archivo ZIP a LMS compatible con SCORM"
  ))
}

#' Configurar integración con LMS específico
#' 
#' Configura parámetros específicos para integración con un LMS
#' 
#' @param lms_tipo Tipo de LMS ("moodle", "canvas", "blackboard", etc.)
#' @param configuracion_lms Lista con configuración específica del LMS
#' @return Lista con configuración optimizada
#' @export
#' @examples
#' \dontrun{
#' config <- configurar_integracion_lms("moodle", list(url = "https://mi-moodle.edu.co"))
#' }
configurar_integracion_lms <- function(lms_tipo, configuracion_lms = list()) {
  
  # Configuraciones optimizadas por LMS
  configuraciones_lms <- list(
    moodle = list(
      formato_preferido = "xml",
      soporte_latex = TRUE,
      soporte_imagenes = TRUE,
      max_opciones = 10,
      tipos_pregunta = c("multichoice", "numerical", "shortanswer", "essay"),
      configuracion_especial = list(
        single = "true",
        shuffleanswers = "1",
        answernumbering = "abc"
      )
    ),
    canvas = list(
      formato_preferido = "qti",
      soporte_latex = FALSE,
      soporte_imagenes = TRUE,
      max_opciones = 8,
      tipos_pregunta = c("multiple_choice_question", "numerical_question", "short_answer_question"),
      configuracion_especial = list(
        points_possible = 1,
        question_type = "multiple_choice_question"
      )
    ),
    blackboard = list(
      formato_preferido = "zip",
      soporte_latex = FALSE,
      soporte_imagenes = TRUE,
      max_opciones = 6,
      tipos_pregunta = c("MC", "NUM", "FIB", "ESS"),
      configuracion_especial = list(
        feedback = "immediate",
        partial_credit = "true"
      )
    ),
    brightspace = list(
      formato_preferido = "qti",
      soporte_latex = TRUE,
      soporte_imagenes = TRUE,
      max_opciones = 12,
      tipos_pregunta = c("MultipleChoice", "Numeric", "ShortAnswer"),
      configuracion_especial = list(
        randomize = "true",
        show_feedback = "true"
      )
    )
  )
  
  # Obtener configuración base para el LMS
  if (lms_tipo %in% names(configuraciones_lms)) {
    config_base <- configuraciones_lms[[lms_tipo]]
  } else {
    warning("LMS no reconocido. Usando configuración genérica.")
    config_base <- configuraciones_lms$moodle  # Configuración por defecto
  }
  
  # Combinar con configuración personalizada
  config_final <- modifyList(config_base, configuracion_lms)
  
  # Agregar metadatos
  config_final$lms_tipo <- lms_tipo
  config_final$fecha_configuracion <- Sys.time()
  config_final$version_icfes <- utils::packageVersion("ICFESMathExams")
  
  return(config_final)
}

#' Generar reporte de compatibilidad LMS
#' 
#' Analiza ejercicios y genera reporte de compatibilidad con diferentes LMS
#' 
#' @param ejercicios Lista de ejercicios a analizar
#' @param lms_objetivo Vector de LMS a evaluar
#' @return Lista con reporte de compatibilidad
#' @export
#' @examples
#' \dontrun{
#' reporte <- generar_reporte_compatibilidad_lms(ejercicios, c("moodle", "canvas"))
#' }
generar_reporte_compatibilidad_lms <- function(ejercicios, 
                                              lms_objetivo = c("moodle", "canvas", "blackboard")) {
  
  reporte <- list(
    resumen_general = list(
      num_ejercicios = length(ejercicios),
      fecha_analisis = Sys.time(),
      lms_analizados = lms_objetivo
    ),
    compatibilidad_por_lms = list(),
    recomendaciones = list(),
    ejercicios_problematicos = list()
  )
  
  # Analizar compatibilidad para cada LMS
  for (lms in lms_objetivo) {
    config_lms <- configurar_integracion_lms(lms)
    compatibilidad <- analizar_compatibilidad_ejercicios(ejercicios, config_lms)
    
    reporte$compatibilidad_por_lms[[lms]] <- compatibilidad
    
    # Identificar ejercicios problemáticos
    problematicos <- which(!compatibilidad$ejercicios_compatibles)
    if (length(problematicos) > 0) {
      reporte$ejercicios_problematicos[[lms]] <- problematicos
    }
  }
  
  # Generar recomendaciones generales
  reporte$recomendaciones <- generar_recomendaciones_compatibilidad(reporte)
  
  class(reporte) <- "reporte_compatibilidad_lms"
  return(reporte)
}

#' Funciones auxiliares para integración LMS
#' 
#' @keywords internal

# Determinar tipo de pregunta Moodle
determinar_tipo_moodle <- function(ejercicio) {
  if (!is.null(ejercicio$opciones) && length(ejercicio$opciones) > 1) {
    return("multichoice")
  } else if (!is.null(ejercicio$respuesta_correcta) && is.numeric(ejercicio$respuesta_correcta)) {
    return("numerical")
  } else {
    return("shortanswer")
  }
}

# Generar texto de pregunta en HTML
generar_texto_pregunta_html <- function(ejercicio) {
  html <- paste0("<p>", ejercicio$enunciado %||% "Pregunta sin enunciado", "</p>")
  
  # Agregar contexto si existe
  if (!is.null(ejercicio$contexto_icfes)) {
    html <- paste0(html, "<p><em>Contexto: ", ejercicio$contexto_icfes, "</em></p>")
  }
  
  # Agregar procedimiento si existe
  if (!is.null(ejercicio$procedimiento)) {
    html <- paste0(html, "<details><summary>Ayuda</summary>",
                   "<p>", paste(ejercicio$procedimiento, collapse = "<br>"), "</p>",
                   "</details>")
  }
  
  return(html)
}

# Generar retroalimentación en HTML
generar_retroalimentacion_html <- function(ejercicio) {
  if (!is.null(ejercicio$solucion)) {
    return(paste0("<p><strong>Solución:</strong> ", ejercicio$solucion, "</p>"))
  }
  return("<p>Ejercicio completado.</p>")
}

#' Generar opciones de respuesta múltiple en XML para Moodle
#'
#' @param ejercicio Ejercicio con opciones de respuesta
#' @param config Configuración de exportación
#' @return Vector de líneas XML
#' @keywords internal
generar_opciones_multichoice_xml <- function(ejercicio, config) {

  # Obtener opciones y respuesta correcta
  opciones <- ejercicio$opciones %||% c()
  respuesta_correcta <- ejercicio$respuesta_correcta %||% opciones[1]

  # Si no hay opciones, crear algunas básicas
  if (length(opciones) == 0) {
    opciones <- c(respuesta_correcta, respuesta_correcta + 1, respuesta_correcta - 1, respuesta_correcta + 2)
  }

  xml_lines <- c()

  # Configuración de respuesta múltiple
  xml_lines <- c(xml_lines,
    '    <single>true</single>',
    '    <shuffleanswers>1</shuffleanswers>',
    '    <answernumbering>abc</answernumbering>'
  )

  # Generar cada opción
  for (i in seq_along(opciones)) {
    opcion <- opciones[i]
    es_correcta <- (opcion == respuesta_correcta)
    fraccion <- if (es_correcta) "100" else "0"

    xml_lines <- c(xml_lines,
      '    <answer fraction="' %+% fraccion %+% '" format="html">',
      '      <text><![CDATA[<p>' %+% as.character(opcion) %+% '</p>]]></text>',
      '      <feedback format="html">',
      '        <text><![CDATA[<p>' %+%
        if (es_correcta) "¡Correcto!" else "Incorrecto. Revisa tu procedimiento." %+%
        '</p>]]></text>',
      '      </feedback>',
      '    </answer>'
    )
  }

  return(xml_lines)
}

#' Generar respuesta numérica en XML para Moodle
#'
#' @param ejercicio Ejercicio con respuesta numérica
#' @return Vector de líneas XML
#' @keywords internal
generar_respuesta_numerica_xml <- function(ejercicio) {

  # Obtener respuesta correcta
  respuesta <- ejercicio$respuesta_correcta %||% 0
  tolerancia <- ejercicio$tolerancia %||% 0.01

  xml_lines <- c(
    '    <answer fraction="100" format="moodle_auto_format">',
    '      <text>' %+% as.character(respuesta) %+% '</text>',
    '      <tolerance>' %+% as.character(tolerancia) %+% '</tolerance>',
    '      <feedback format="html">',
    '        <text><![CDATA[<p>¡Correcto!</p>]]></text>',
    '      </feedback>',
    '    </answer>',
    '    <answer fraction="0" format="moodle_auto_format">',
    '      <text>*</text>',
    '      <tolerance></tolerance>',
    '      <feedback format="html">',
    '        <text><![CDATA[<p>Incorrecto. Revisa tu procedimiento.</p>]]></text>',
    '      </feedback>',
    '    </answer>'
  )

  return(xml_lines)
}

#' Exportar a formato Blackboard (implementación básica)
#'
#' @param ejercicios Lista de ejercicios
#' @param config Configuración de exportación
#' @param directorio Directorio de salida
#' @return Lista con información de exportación
#' @keywords internal
exportar_blackboard_zip <- function(ejercicios, config, directorio) {

  # Crear archivo de texto simple para Blackboard
  archivo_bb <- file.path(directorio, paste0(config$nombre_curso, "_blackboard.txt"))

  contenido <- c(
    paste("# Examen:", config$nombre_curso),
    paste("# Categoría:", config$categoria),
    paste("# Fecha de generación:", Sys.time()),
    "",
    "# Instrucciones: Importar manualmente a Blackboard",
    ""
  )

  # Agregar cada ejercicio
  for (i in seq_along(ejercicios)) {
    ejercicio <- ejercicios[[i]]
    contenido <- c(contenido,
      paste("## Pregunta", i),
      paste("Enunciado:", ejercicio$enunciado %||% "Sin enunciado"),
      paste("Respuesta correcta:", ejercicio$respuesta_correcta %||% "No especificada"),
      ""
    )
  }

  writeLines(contenido, archivo_bb)

  return(list(
    formato = "blackboard",
    archivo_principal = archivo_bb,
    num_ejercicios = length(ejercicios),
    estado = "exportado_texto"
  ))
}

#' Exportar a formato Canvas (implementación básica)
#'
#' @param ejercicios Lista de ejercicios
#' @param config Configuración de exportación
#' @param directorio Directorio de salida
#' @return Lista con información de exportación
#' @keywords internal
exportar_canvas_qti <- function(ejercicios, config, directorio) {

  # Usar la exportación QTI existente (Canvas soporta QTI)
  resultado_qti <- exportar_qti_xml(ejercicios, config, directorio)

  # Renombrar para Canvas
  archivo_canvas <- file.path(directorio, paste0(config$nombre_curso, "_canvas.xml"))
  if (file.exists(resultado_qti$archivo_principal)) {
    file.copy(resultado_qti$archivo_principal, archivo_canvas)
  }

  return(list(
    formato = "canvas",
    archivo_principal = archivo_canvas,
    num_ejercicios = length(ejercicios),
    estado = "exportado_qti"
  ))
}

#' Exportar a formato Brightspace (implementación básica)
#'
#' @param ejercicios Lista de ejercicios
#' @param config Configuración de exportación
#' @param directorio Directorio de salida
#' @return Lista con información de exportación
#' @keywords internal
exportar_brightspace_zip <- function(ejercicios, config, directorio) {

  # Crear archivo CSV para Brightspace
  archivo_bs <- file.path(directorio, paste0(config$nombre_curso, "_brightspace.csv"))

  # Crear estructura CSV básica
  datos_csv <- data.frame(
    Pregunta = sapply(seq_along(ejercicios), function(i) {
      paste("Pregunta", i, ":", ejercicios[[i]]$enunciado %||% "Sin enunciado")
    }),
    Respuesta_Correcta = sapply(ejercicios, function(e) e$respuesta_correcta %||% "No especificada"),
    Tipo = sapply(ejercicios, function(e) e$tipo %||% "general"),
    stringsAsFactors = FALSE
  )

  write.csv(datos_csv, archivo_bs, row.names = FALSE)

  return(list(
    formato = "brightspace",
    archivo_principal = archivo_bs,
    num_ejercicios = length(ejercicios),
    estado = "exportado_csv"
  ))
}

#' Generar referencia de ítem para QTI
#'
#' @param ejercicio Ejercicio individual
#' @param indice Índice del ejercicio
#' @return Vector de líneas XML
#' @keywords internal
generar_item_reference_qti <- function(ejercicio, indice) {
  item_id <- paste0("item_", sprintf("%03d", indice))
  return(paste0('      <assessmentItemRef identifier="', item_id, '" href="', item_id, '.xml"/>'))
}

#' Crear archivos de ítems individuales para QTI
#'
#' @param ejercicios Lista de ejercicios
#' @param directorio Directorio de salida
#' @return Vector de nombres de archivos creados
#' @keywords internal
crear_archivos_items_qti <- function(ejercicios, directorio) {

  archivos_creados <- c()

  for (i in seq_along(ejercicios)) {
    ejercicio <- ejercicios[[i]]
    item_id <- paste0("item_", sprintf("%03d", i))
    archivo_item <- file.path(directorio, paste0(item_id, ".xml"))

    # Crear contenido básico del ítem QTI
    item_content <- c(
      '<?xml version="1.0" encoding="UTF-8"?>',
      '<assessmentItem xmlns="http://www.imsglobal.org/xsd/imsqti_v2p1"',
      '                xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"',
      '                xsi:schemaLocation="http://www.imsglobal.org/xsd/imsqti_v2p1 http://www.imsglobal.org/xsd/qti/qtiv2p1/imsqti_v2p1.xsd"',
      paste0('                identifier="', item_id, '"'),
      paste0('                title="Ejercicio ', i, '"'),
      '                adaptive="false" timeDependent="false">',
      '',
      '  <responseDeclaration identifier="RESPONSE" cardinality="single" baseType="identifier">',
      '    <correctResponse>',
      paste0('      <value>A</value>'),  # Simplificado
      '    </correctResponse>',
      '  </responseDeclaration>',
      '',
      '  <itemBody>',
      paste0('    <p>', ejercicio$enunciado %||% "Pregunta sin enunciado", '</p>'),
      '  </itemBody>',
      '',
      '</assessmentItem>'
    )

    writeLines(item_content, archivo_item, useBytes = TRUE)
    archivos_creados <- c(archivos_creados, archivo_item)
  }

  return(archivos_creados)
}

#' Crear manifiesto IMS para QTI
#'
#' @param config Configuración de exportación
#' @param directorio Directorio de salida
#' @param archivo_qti Archivo QTI principal
#' @param archivos_items Archivos de ítems individuales
#' @return Ruta del archivo de manifiesto
#' @keywords internal
crear_manifiesto_ims <- function(config, directorio, archivo_qti, archivos_items) {

  archivo_manifiesto <- file.path(directorio, "imsmanifest.xml")

  # Crear contenido básico del manifiesto
  manifest_content <- c(
    '<?xml version="1.0" encoding="UTF-8"?>',
    '<manifest xmlns="http://www.imsglobal.org/xsd/imscp_v1p1"',
    '          xmlns:imsmd="http://www.imsglobal.org/xsd/imsmd_v1p2"',
    '          xmlns:imsqti="http://www.imsglobal.org/xsd/imsqti_v2p1"',
    '          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"',
    '          xsi:schemaLocation="http://www.imsglobal.org/xsd/imscp_v1p1 http://www.imsglobal.org/xsd/qti/qtiv2p1/imscp_v1p1.xsd"',
    paste0('          identifier="MANIFEST_', format(Sys.time(), "%Y%m%d_%H%M%S"), '">'),
    '',
    '  <metadata>',
    '    <schema>IMS QTI</schema>',
    '    <schemaversion>2.1</schemaversion>',
    '  </metadata>',
    '',
    '  <organizations/>',
    '',
    '  <resources>',
    paste0('    <resource identifier="assessment" type="imsqti_test_xmlv2p1" href="', basename(archivo_qti), '">'),
    paste0('      <file href="', basename(archivo_qti), '"/>'),
    '    </resource>',
    '  </resources>',
    '',
    '</manifest>'
  )

  writeLines(manifest_content, archivo_manifiesto, useBytes = TRUE)
  return(archivo_manifiesto)
}

#' Crear archivo de configuración para Moodle
#'
#' @param config Configuración de exportación
#' @param directorio Directorio de salida
#' @return Ruta del archivo de configuración
#' @keywords internal
crear_archivo_configuracion_moodle <- function(config, directorio) {

  archivo_config <- file.path(directorio, "configuracion_moodle.txt")

  # Crear contenido de configuración
  config_content <- c(
    "# Configuración para importar a Moodle",
    paste("# Generado:", Sys.time()),
    "",
    "## INSTRUCCIONES DE IMPORTACIÓN:",
    "1. Ir a Administración del curso > Banco de preguntas",
    "2. Seleccionar 'Importar'",
    "3. Elegir formato 'Moodle XML'",
    paste("4. Subir el archivo:", paste0(config$nombre_curso, "_moodle.xml")),
    "5. Configurar las opciones según se indica abajo",
    "",
    "## CONFIGURACIÓN RECOMENDADA:",
    paste("Nombre del curso:", config$nombre_curso %||% "Matemáticas ICFES"),
    paste("Categoría:", config$categoria %||% "Exámenes"),
    paste("Tiempo límite:", config$tiempo_limite %||% 90, "minutos"),
    paste("Intentos permitidos:", config$intentos_permitidos %||% 2),
    paste("Calificación para aprobar:", config$calificacion_minima %||% 70, "%"),
    "",
    "## CONFIGURACIÓN AVANZADA:",
    paste("Barajar preguntas:", config$barajar_preguntas %||% "Sí"),
    paste("Barajar opciones:", config$barajar_opciones %||% "Sí"),
    paste("Mostrar retroalimentación:", config$mostrar_feedback %||% "Después del envío"),
    paste("Navegación secuencial:", config$navegacion_secuencial %||% "No"),
    "",
    "## NOTAS IMPORTANTES:",
    "- Verificar que todas las preguntas se importaron correctamente",
    "- Revisar la configuración de puntuación",
    "- Probar el examen antes de publicarlo",
    "- Configurar fechas de disponibilidad según necesidades"
  )

  writeLines(config_content, archivo_config)
  return(archivo_config)
}

#' Analizar compatibilidad de ejercicios con LMS
#'
#' @param ejercicios Lista de ejercicios a analizar
#' @param config_lms Configuración del LMS
#' @return Lista con análisis de compatibilidad
#' @keywords internal
analizar_compatibilidad_ejercicios <- function(ejercicios, config_lms) {

  num_ejercicios <- length(ejercicios)
  ejercicios_compatibles <- rep(TRUE, num_ejercicios)
  problemas_encontrados <- list()

  # Analizar cada ejercicio
  for (i in seq_along(ejercicios)) {
    ejercicio <- ejercicios[[i]]
    problemas_ejercicio <- c()

    # Verificar tipo de pregunta
    tipo_pregunta <- ejercicio$tipo_pregunta %||% "multichoice"
    if (!tipo_pregunta %in% (config_lms$tipos_pregunta %||% c("multichoice"))) {
      problemas_ejercicio <- c(problemas_ejercicio,
                              paste("Tipo de pregunta no soportado:", tipo_pregunta))
      ejercicios_compatibles[i] <- FALSE
    }

    # Verificar longitud del enunciado
    enunciado <- ejercicio$enunciado %||% ""
    max_longitud <- config_lms$max_longitud_enunciado %||% 5000
    if (nchar(enunciado) > max_longitud) {
      problemas_ejercicio <- c(problemas_ejercicio,
                              "Enunciado demasiado largo")
    }

    # Verificar número de opciones
    opciones <- ejercicio$opciones %||% c()
    max_opciones <- config_lms$max_opciones %||% 10
    if (length(opciones) > max_opciones) {
      problemas_ejercicio <- c(problemas_ejercicio,
                              "Demasiadas opciones de respuesta")
    }

    # Verificar caracteres especiales
    texto_completo <- paste(enunciado, paste(opciones, collapse = " "))
    soporta_unicode <- config_lms$soporta_unicode %||% TRUE
    # Verificar caracteres no ASCII de forma más simple
    tiene_unicode <- any(utf8ToInt(texto_completo) > 127)
    if (tiene_unicode && !soporta_unicode) {
      problemas_ejercicio <- c(problemas_ejercicio,
                              "Contiene caracteres especiales no soportados")
    }

    if (length(problemas_ejercicio) > 0) {
      problemas_encontrados[[i]] <- problemas_ejercicio
    }
  }

  return(list(
    ejercicios_compatibles = ejercicios_compatibles,
    porcentaje_compatibilidad = mean(ejercicios_compatibles) * 100,
    problemas_encontrados = problemas_encontrados,
    num_ejercicios_analizados = num_ejercicios,
    num_ejercicios_compatibles = sum(ejercicios_compatibles)
  ))
}

#' Generar recomendaciones de compatibilidad
#'
#' @param reporte Reporte de compatibilidad
#' @return Lista de recomendaciones
#' @keywords internal
generar_recomendaciones_compatibilidad <- function(reporte) {

  recomendaciones <- c()

  # Analizar compatibilidad general
  compatibilidades <- sapply(reporte$compatibilidad_por_lms, function(x) x$porcentaje_compatibilidad)
  compatibilidad_promedio <- mean(compatibilidades, na.rm = TRUE)

  if (compatibilidad_promedio < 70) {
    recomendaciones <- c(recomendaciones,
                        "⚠️ Compatibilidad baja. Revisar ejercicios problemáticos.")
  } else if (compatibilidad_promedio < 90) {
    recomendaciones <- c(recomendaciones,
                        "✅ Compatibilidad aceptable. Considerar mejoras menores.")
  } else {
    recomendaciones <- c(recomendaciones,
                        "✅ Excelente compatibilidad con todos los LMS.")
  }

  # Recomendaciones específicas por LMS
  for (lms in names(reporte$compatibilidad_por_lms)) {
    compat <- reporte$compatibilidad_por_lms[[lms]]
    if (compat$porcentaje_compatibilidad < 80) {
      recomendaciones <- c(recomendaciones,
                          paste("📋", toupper(lms), ": Revisar ejercicios con problemas"))
    }
  }

  # Recomendaciones generales
  recomendaciones <- c(recomendaciones,
    "💡 Probar importación en entorno de prueba antes de producción",
    "📝 Revisar configuraciones específicas de cada LMS",
    "🔄 Actualizar ejercicios según feedback de importación"
  )

  return(recomendaciones)
}

#' Método print para reportes de compatibilidad LMS
#'
#' @param x Objeto de clase reporte_compatibilidad_lms
#' @param ... Argumentos adicionales
#' @return Invisible(x)
#' @export
print.reporte_compatibilidad_lms <- function(x, ...) {

  # Función simple sin sprintf ni operaciones complejas
  cat("\n=== REPORTE COMPATIBILIDAD LMS ===\n")
  cat("Ejercicios analizados: ")
  cat(x$resumen_general$num_ejercicios)
  cat("\n")

  cat("LMS evaluados: ")
  cat(paste(x$resumen_general$lms_analizados, collapse = ", "))
  cat("\n")

  cat("\n--- Compatibilidad por LMS ---\n")

  # Iterar de forma muy simple
  for (lms in names(x$compatibilidad_por_lms)) {
    cat(toupper(lms))
    cat(": ")

    compat <- x$compatibilidad_por_lms[[lms]]
    if (!is.null(compat) && !is.null(compat$porcentaje_compatibilidad)) {
      if (compat$porcentaje_compatibilidad >= 80) {
        cat("Compatible")
      } else {
        cat("Problemas")
      }
      cat(" (")
      cat(round(compat$porcentaje_compatibilidad, 1))
      cat("%)")
    } else {
      cat("Error en análisis")
    }
    cat("\n")
  }

  cat("\n--- Recomendaciones ---\n")
  if (length(x$recomendaciones) > 0) {
    for (rec in x$recomendaciones) {
      cat("• ")
      cat(rec)
      cat("\n")
    }
  } else {
    cat("• No hay recomendaciones específicas\n")
  }

  cat("\n")
  invisible(x)
}

# Operador de concatenación para XML
`%+%` <- function(x, y) paste0(x, y)

# Operador %||% para valores por defecto
`%||%` <- function(x, y) if (is.null(x)) y else x
